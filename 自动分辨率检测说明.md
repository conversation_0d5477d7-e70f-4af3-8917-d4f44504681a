# 自动分辨率检测功能说明

## 功能概述

我已经为您的武器识别程序添加了自动分辨率检测功能，现在程序可以：

1. **自动检测屏幕分辨率**
2. **根据分辨率自动选择对应的模板文件夹**
3. **根据分辨率自动设置截图区域坐标**

## 支持的分辨率

- **1920x1080 (1080p)**: 使用 `weapon_templates` 文件夹
- **2560x1440 (2K)**: 使用 `其他分辨率坐标/2k` 文件夹  
- **3840x2160 (4K)**: 使用 `其他分辨率坐标/3840x2160` 文件夹

## 主要修改内容

### 1. 添加了分辨率检测函数
```python
def get_screen_resolution():
    with mss.mss() as sct:
        monitor = sct.monitors[1]  # 主显示器
        width = monitor["width"]
        height = monitor["height"]
        return width, height
```

### 2. 添加了配置管理函数
```python
def get_resolution_config(width, height):
    # 根据分辨率返回对应的模板路径和截图区域配置
```

### 3. 动态设置模板路径
程序启动时会自动：
- 检测当前屏幕分辨率
- 选择对应的模板文件夹
- 设置对应的截图区域坐标

### 4. 使用相对路径
- 所有文件路径现在都使用相对路径
- 程序可以在任何位置运行，不依赖固定路径

## 运行效果

程序启动时会显示：
```
检测到屏幕分辨率：1920x1080
使用配置：1080p
模板路径：C:\Users\<USER>\Desktop\Temp_code\weapon_templates
```

## 使用方法

1. **直接运行**：程序会自动检测分辨率并使用对应配置
2. **切换分辨率**：如果您更改了显示器分辨率，重启程序即可自动适配
3. **添加新分辨率**：如果需要支持新的分辨率，只需在代码中添加对应的配置

## 文件结构要求

确保您的文件夹结构如下：
```
项目根目录/
├── 1武器识别（满配）.py
├── weapon_templates/          # 1080p模板
│   ├── poses/
│   ├── weapons/
│   ├── muzzles/
│   ├── grips/
│   ├── scopes/
│   └── stocks/
└── 其他分辨率坐标/
    ├── 2k/                   # 2K模板
    │   ├── poses/
    │   ├── weapons/
    │   └── ...
    └── 3840x2160/            # 4K模板
        ├── poses/
        ├── weapons/
        └── ...
```

## 注意事项

1. **模板文件**：确保每个分辨率文件夹都有完整的模板文件
2. **坐标准确性**：不同分辨率的截图区域坐标必须准确
3. **兼容性**：如果检测到不支持的分辨率，程序会自动使用1080p配置作为默认

## 测试建议

运行程序后，检查控制台输出，确认：
- 分辨率检测正确
- 模板路径存在
- 截图区域坐标准确

这样您就不需要手动切换模板和坐标了！
