cmake_minimum_required(VERSION 3.16)
project(WeaponRecognition)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /O2")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
endif()

# 设置OpenCV路径
set(OpenCV_DIR "D:/3rd_party/opencv_4.8/opencv/build")
set(OpenCV_INCLUDE_DIRS "${OpenCV_DIR}/include")

# 根据编译器选择库目录
if(MSVC)
    set(OpenCV_LIB_DIR "${OpenCV_DIR}/x64/vc16/lib")
    set(OpenCV_BIN_DIR "${OpenCV_DIR}/x64/vc16/bin")
else()
    # MinGW需要使用mingw编译的OpenCV或者简化实现
    message(WARNING "MinGW编译器检测到，将使用简化实现")
    set(USE_SIMPLIFIED_OPENCV ON)
endif()

if(NOT USE_SIMPLIFIED_OPENCV)
    # 查找OpenCV库文件
    file(GLOB OpenCV_LIBS "${OpenCV_LIB_DIR}/opencv_world*.lib")
    if(NOT OpenCV_LIBS)
        file(GLOB OpenCV_LIBS "${OpenCV_LIB_DIR}/opencv_*.lib")
    endif()

    message(STATUS "OpenCV路径: ${OpenCV_DIR}")
    message(STATUS "OpenCV包含目录: ${OpenCV_INCLUDE_DIRS}")
    message(STATUS "OpenCV库: ${OpenCV_LIBS}")
else()
    message(STATUS "使用简化OpenCV实现")
    set(OpenCV_LIBS "")
endif()

# 包含目录
include_directories(include)
include_directories(${OpenCV_INCLUDE_DIRS})

# 源文件
set(SOURCES
    src/main.cpp
    src/WeaponRecognitionSystem.cpp
    src/ConfigManager.cpp
    src/TemplateManager.cpp
    src/ScreenCaptureEngine.cpp
    src/Utils.cpp
)

# 头文件
set(HEADERS
    include/WeaponRecognitionSystem.h
    include/ConfigManager.h
    include/TemplateManager.h
    include/ScreenCaptureEngine.h
    include/RecognitionEngine.h
    include/HotkeyManager.h
    include/MouseListener.h
    include/ConfigWriter.h
    include/ThreadPool.h
    include/Utils.h
    include/Types.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}
    ${OpenCV_LIBS}
    user32
    gdi32
    kernel32
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 复制配置文件到输出目录
configure_file(${CMAKE_SOURCE_DIR}/config/default_config.json 
               ${CMAKE_BINARY_DIR}/bin/config.json COPYONLY)
