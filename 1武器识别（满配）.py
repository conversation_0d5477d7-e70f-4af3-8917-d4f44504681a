import cv2
import numpy as np
import mss
import time
import keyboard
from tkinter import Tk, Label
import ctypes
from concurrent.futures import ThreadPoolExecutor
import json
import pyautogui
from pynput import mouse  # 引入pynput库
import os
import sys

# 设置文件系统编码
if sys.platform.startswith('win'):
    import locale
    locale.setlocale(locale.LC_ALL, 'Chinese (Simplified)_China.936')

# 获取屏幕分辨率 - 使用Windows API
def get_screen_resolution():
    """使用Windows API自动检测主显示器分辨率"""
    try:
        user32 = ctypes.windll.user32
        width = user32.GetSystemMetrics(0)  # SM_CXSCREEN - 主显示器宽度
        height = user32.GetSystemMetrics(1)  # SM_CYSCREEN - 主显示器高度
        print(f"Windows API检测到主显示器分辨率：{width}x{height}")
        return width, height
    except Exception as e:
        print(f"Windows API检测失败：{e}")
        # 如果Windows API失败，使用默认值
        print("使用默认分辨率：1920x1080")
        return 1920, 1080

# 根据分辨率获取配置 - 使用nc文件夹的新结构
def get_resolution_config(width, height):
    base_path = os.path.dirname(os.path.abspath(__file__))
    nc_path = os.path.join(base_path, "nc").replace("\\", "/")

    # 构建分辨率文件夹路径
    resolution_folder = f"{width}x{height}"
    config_path = f"{nc_path}/{resolution_folder}"
    config_file = f"{config_path}/config.json"
    template_path = f"{config_path}/weapon_templates"

    # 检查配置文件是否存在
    if not os.path.exists(config_file):
        print(f"警告：未找到分辨率 {resolution_folder} 的配置文件：{config_file}")
        # 如果没有找到对应分辨率，尝试使用最接近的分辨率
        available_resolutions = get_available_resolutions(nc_path)
        closest_resolution = find_closest_resolution(width, height, available_resolutions)
        if closest_resolution:
            print(f"使用最接近的分辨率配置：{closest_resolution}")
            config_path = f"{nc_path}/{closest_resolution}"
            config_file = f"{config_path}/config.json"
            template_path = f"{config_path}/weapon_templates"
        else:
            print("使用默认1920x1080配置")
            config_path = f"{nc_path}/1920x1080"
            config_file = f"{config_path}/config.json"
            template_path = f"{config_path}/weapon_templates"

    # 读取JSON配置文件
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        # 转换regions格式：从[x, y, w, h]转换为(x, y, w, h)
        regions = {}
        for key, value in config_data["regions"].items():
            regions[key] = tuple(value)

        return {
            "name": resolution_folder,
            "template_path": template_path,
            "regions": regions,
            "config_data": config_data  # 保存完整配置数据
        }

    except Exception as e:
        print(f"读取配置文件失败：{e}")
        # 返回默认配置
        return {
            "name": "default",
            "template_path": f"{nc_path}/1920x1080/weapon_templates",
            "regions": {
                "poses": (711, 976, 30, 50),
                "weapon_name_rifle": (1371, 95, 70, 30),
                "weapon_name_sniper": (1371, 321, 70, 30),
                "muzzles_rifle": (1356, 242, 20, 20),
                "muzzles_sniper": (1356, 468, 20, 20),
                "grips_rifle": (1448, 253, 20, 20),
                "grips_sniper": (1448, 479, 20, 20),
                "scopes_rifle": (1631, 121, 20, 20),
                "scopes_sniper": (1631, 347, 20, 20),
                "stocks_rifle": (1765, 255, 20, 20),
                "stocks_sniper": (1765, 481, 20, 20),
            }
        }

# 获取可用的分辨率配置
def get_available_resolutions(nc_path):
    available = []
    try:
        for item in os.listdir(nc_path):
            item_path = os.path.join(nc_path, item)
            if os.path.isdir(item_path) and 'x' in item:
                # 检查是否有config.json文件
                config_file = os.path.join(item_path, "config.json")
                if os.path.exists(config_file):
                    available.append(item)
    except:
        pass
    return available

# 找到最接近的分辨率
def find_closest_resolution(target_width, target_height, available_resolutions):
    if not available_resolutions:
        return None

    min_distance = float('inf')
    closest = None

    for res in available_resolutions:
        try:
            w, h = map(int, res.split('x'))
            # 计算欧几里得距离
            distance = ((target_width - w) ** 2 + (target_height - h) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                closest = res
        except:
            continue

    return closest

# 自动检测分辨率并设置配置
screen_width, screen_height = get_screen_resolution()
current_config = get_resolution_config(screen_width, screen_height)
print(f"使用分辨率：{screen_width}x{screen_height}")
print(f"使用配置：{current_config['name']}")
print(f"模板路径：{current_config['template_path']}")

# 动态设置模板路径 - 适配nc文件夹结构
template_base = current_config['template_path']
template_dirs = {
    "poses": f"{template_base}/poses/",
    "weapons": f"{template_base}/weapons/",
    "muzzles": f"{template_base}/muzzles/",
    "grips": f"{template_base}/grips/",
    "scopes": f"{template_base}/scopes/",
    "stocks": f"{template_base}/stocks/",
    "bag": f"{template_base}/bag/",      # 新增背包检测
    "car": f"{template_base}/car/",      # 新增载具检测
    "shoot": f"{template_base}/shoot/",  # 新增射击检测
}

# 动态扫描模板文件名
def scan_template_files(template_dirs):
    """动态扫描模板文件夹中的所有PNG文件"""
    template_names = {}

    for category, path in template_dirs.items():
        template_names[category] = []
        try:
            if os.path.exists(path.rstrip('/')):
                for file in os.listdir(path.rstrip('/')):
                    if file.lower().endswith('.png'):
                        # 去掉.png扩展名
                        name = file[:-4]
                        template_names[category].append(name)
                print(f"扫描到 {category}: {len(template_names[category])} 个模板文件")
            else:
                print(f"警告：模板文件夹不存在 {path}")
        except Exception as e:
            print(f"扫描模板文件夹 {category} 时出错: {e}")

    return template_names

# 扫描当前分辨率的模板文件
template_names = scan_template_files(template_dirs)

# 模板分类
pose_names = ["stand", "down"]
weapon_names = ["AKM", "Berry", "AUG", "M416", "ACE32", "G36C", "GROZA", "FAMAS", "M16", "K2", "SCAR", "M249", "DP28", "MG3", "P90", "QBZ", "MK47", "DLG", "SKS", "SLR", "MINI", "MK12", "QBU", "VSS", "MK14", "JS9", "MP5", "MP9", "UMP", "TOM", "UZI", "VECTOR", "PP19"]
muzzle_names = ["bc1", "bc2", "bc3", "xy1", "xy2", "xy3", "xx", "xx1", "zt"]
grip_names = ["angle", "light", "line", "thumb", "red"]
scope_names = ["reddot", "quanxi", "x2", "x3", "x4", "x6", "x8"]
stock_names = ["normal", "heavy", "pg"]

# 创建映射字典
name_map = {
    "bc1": "补偿1",
    "bc2": "补偿2",
    "bc3": "补偿3",
    "xy1": "消焰1",
    "xy2": "消焰2",
    "xy3": "消焰3",
    "xx": "消声1",
    "xx1": "消声2",
    "zt": "抑制",
    "angle": "三角",
    "light": "轻型",
    "line": "垂直",
    "thumb": "拇指",
    "red": "红握",
    "reddot": "红点",
    "quanxi": "全息",
    "x2": "2倍",
    "x3": "3倍",
    "x4": "4倍",
    "x6": "6倍",
    "x8": "8倍",
    "normal": "枪托",
    "heavy": "重型",
    "pg": "屁股",
    "none": "无",
    "open": "open",
    "stand": "站立",  # 新增poses映射
    "down": "蹲下",
    "lie": "卧倒"
}

# 加载模板文件 - 使用动态扫描的文件名
templates = {}
for category, path in template_dirs.items():
    templates[category] = {}

    # 使用动态扫描的模板文件名
    if category in template_names:
        names = template_names[category]
    else:
        names = []

    for template_name in names:
        # 使用原始字符串和规范化路径
        template_path = os.path.normpath(f"{path}{template_name}.png")
        try:
            template = cv2.imread(template_path, 0)
            if template is not None:
                templates[category][template_name] = template
            # 静默处理缺失的模板文件
        except Exception as e:
            # 只在发生异常时显示错误信息
            print(f"错误：加载模板文件 {template_path} 时出错: {e}")

print(f"模板加载完成！总共加载了 {sum(len(templates[cat]) for cat in templates)} 个模板文件")

# 灰度处理
def convert_to_gray(image):
    return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# 自适应二值化处理
def adaptive_threshold(image):
    return cv2.adaptiveThreshold(image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                 cv2.THRESH_BINARY, 11, 2)

# 匹配图像
def match_template(frame, template, method=cv2.TM_CCOEFF_NORMED):
    result = cv2.matchTemplate(frame, template, method)
    if method == cv2.TM_SQDIFF_NORMED:
        min_val, _, _, _ = cv2.minMaxLoc(result)
        return 1 - min_val
    else:
        _, max_val, _, _ = cv2.minMaxLoc(result)
        return max_val

def capture_screen(region, gray=True, threshold=False):
    with mss.mss() as sct:
        monitor = {"top": region[1], "left": region[0], "width": region[2], "height": region[3]}
        screenshot = np.array(sct.grab(monitor))

    frame = cv2.cvtColor(screenshot, cv2.COLOR_BGRA2BGR) # mss默认RGBA，需要转为BGR
    if gray:
        frame = convert_to_gray(frame)  # 转换为灰度
    if threshold:
        frame = adaptive_threshold(frame)  # 应用自适应阈值
    return frame

def identify_from_templates(frame, templates, threshold=0.5):
    best_match = None
    max_score = threshold  # 初始化最大分数为阈值

    for name, template in templates.items():
        match_score = match_template(frame, template)
        if match_score > max_score:
            max_score = match_score
            best_match = name

    return best_match

def translate_name(name):
    return name_map.get(name, name)

def display_results(results, current_weapon):
    display_text = f'{translate_name(results.get("weapon_name_" + current_weapon, ""))} ' \
                   f'{translate_name(results.get("muzzles_" + current_weapon, ""))} ' \
                   f'{translate_name(results.get("grips_" + current_weapon, ""))} ' \
                   f'{translate_name(results.get("scopes_" + current_weapon, ""))} ' \
                   f'{translate_name(results.get("stocks_" + current_weapon, ""))} '
                   
    label.config(text=display_text)

def process_region(category, region, templates):
    frame = capture_screen(region)

    # 确定模板类别
    if 'poses' in category:
        templates_category = 'poses'
    elif 'weapon_name' in category:
        templates_category = 'weapons'
    elif 'muzzles' in category:
        templates_category = 'muzzles'
    elif 'grips' in category:
        templates_category = 'grips'
    elif 'scopes' in category:
        templates_category = 'scopes'
    elif 'stocks' in category:
        templates_category = 'stocks'
    elif 'bag' in category:
        templates_category = 'bag'
    elif 'car' in category:
        templates_category = 'car'
    elif 'shoot' in category:
        templates_category = 'shoot'
    else:
        # 如果没有匹配的类别，返回空结果
        print(f"警告：未知的类别 {category}")
        return category, ""

    # 检查模板类别是否存在
    if templates_category not in templates or not templates[templates_category]:
        print(f"警告：模板类别 {templates_category} 不存在或为空")
        return category, ""

    result = identify_from_templates(frame, templates[templates_category])
    return category, result

# 全局变量
scope_zoom_values = {
    "rifle": {
        "reddot": 1.0,
        "quanxi": 1.0,
        "x2": 1.0,
        "x3": 1.0,
        "x4": 1.0,
        "x6": 1.0,
        "x8": 1.0
    },
    "sniper": {
        "reddot": 1.0,
        "quanxi": 1.0,
        "x2": 1.0,
        "x3": 1.0,
        "x4": 1.0,
        "x6": 1.0,
        "x8": 1.0
    }
}

# 全局变量
scope_zoom = 1.0
max_zoom = 1.6  # 最大缩放值
min_zoom = 1.0   # 最小缩放值
zoom_step = 0.06  # 每次滚动的增量
right_button_pressed = False  # 右键状态标记

def on_scroll(x, y, dx, dy):
    global scope_zoom

    if right_button_pressed:  # 仅当右键按下时才处理滚轮事件
        current_scope = results.get("scopes_" + current_weapon, "none")
        if current_scope in ["x6", "x8"]:
            if dy > 0:  # 向上滚动，放大
                scope_zoom = min(scope_zoom + zoom_step, max_zoom)  # 增加缩放值，最大值为 max_zoom
            elif dy < 0:  # 向下滚动，缩小
                scope_zoom = max(scope_zoom - zoom_step, min_zoom)  # 减少缩放值，最小值为 min_zoom
            results["scope_zoom"] = round(scope_zoom, 2)  # 保留一位小数

def on_click(x, y, button, pressed):
    global right_button_pressed
    if button == mouse.Button.right:
        right_button_pressed = pressed  # 更新右键按下状态            

def handle_weapon_change():
    global scope_zoom
    current_scope = results.get("scopes_" + current_weapon, "none")
    if current_scope not in ["x6", "x8"]:
        scope_zoom = 1.0  # 默认值
    results["scope_zoom"] = scope_zoom

if __name__ == "__main__":
    # 使用动态配置的截图区域
    regions = current_config['regions']

    is_recognizing = False  # 默认为不识别
    current_weapon = "rifle"  # 默认武器类型为rifle
    previous_weapon = current_weapon  # 记录上一次选择的武器类型
    current_scope = "none"  # 当前瞄准镜类型
    scope_zoom = 1  # 默认缩放值
    results = {}  # 在循环外初始化 results 变量

    def toggle_recognition(event):
        global is_recognizing
        is_recognizing = True
        results["bag"] = "bag"

    def close_recognition(event):
        global is_recognizing
        is_recognizing = False
        results["bag"] = "none"

    keyboard.on_press_key('`', toggle_recognition)
    keyboard.on_press_key('esc', close_recognition)

    # 初始化Tkinter
    root = Tk()
    root.attributes('-topmost', True)
    root.overrideredirect(True)
    root.attributes('-transparentcolor', 'white')

    # 设置窗口大小和位置
    root.geometry('300x20+800+1')
    root.configure(bg='white')

    # 添加标签
    label = Label(root, text="", bg='grey', fg='red', font=('Helvetica', 14, 'bold'))
    label.pack()

    # 使窗口透明
    hwnd = ctypes.windll.user32.GetParent(root.winfo_id())
    ctypes.windll.user32.SetLayeredWindowAttributes(hwnd, 0x00ffffff, 0, 0x00000001)

    # 监听鼠标事件
    listener = mouse.Listener(on_click=on_click, on_scroll=on_scroll)
    listener.start()

    while True:
        # 选择武器类型
        if keyboard.is_pressed('1'):
            current_weapon = "rifle"
        elif keyboard.is_pressed('2'):
            current_weapon = "sniper"
        
        # 选择瞄准镜类型
        current_scope = results.get("scopes_" + current_weapon, "none")

        # 只有当选择的武器类型发生变化时才打印信息
        if current_weapon != previous_weapon:
            print(f"Current weapon: {current_weapon}")
            previous_weapon = current_weapon
            handle_weapon_change()  # 重置 scope_zoom
            display_results(results, current_weapon)

         # ---poses识别逻辑---
        pose_category, pose_result = process_region("poses", regions["poses"], templates)
        results[pose_category] = pose_result
        time.sleep(0.00001)  # pose单独的sleep时间

        if is_recognizing:
            # 仅当按下`键时才进行一次性识别
            with ThreadPoolExecutor() as executor:
                futures = [executor.submit(process_region, category, region, templates) for category, region in regions.items() if 'poses' not in category]
                for future in futures:
                    category, result = future.result()
                    results[category] = result
            is_recognizing = False
            print(f"Detected results: {results}, Current weapon: {current_weapon}")
            display_results(results, current_weapon)

        # 写入 weapon.lua 文件
        weapon_lua_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "weapon.lua")
        with open(weapon_lua_path, "w") as f:
            f.write(f'weapon_name = "{results.get("weapon_name_" + current_weapon, "")}"\n')
            f.write(f'muzzles = "{results.get("muzzles_" + current_weapon, "")}"\n')
            f.write(f'grips = "{results.get("grips_" + current_weapon, "")}"\n')
            f.write(f'scopes = "{results.get("scopes_" + current_weapon, "")}"\n')
            f.write(f'stocks = "{results.get("stocks_" + current_weapon, "")}"\n')
            f.write(f'poses = "{results.get("poses", "")}"\n')
            f.write(f'scope_zoom = "{results.get("scope_zoom", "1")}"\n')
            f.write(f'bag = "{results.get("bag", "none")}"\n')

        # 写入 results.json 文件
        results_json = {
            "weapon_name": translate_name(results.get("weapon_name_" + current_weapon, "")),
            "muzzles": translate_name(results.get("muzzles_" + current_weapon, "")),
            "grips": translate_name(results.get("grips_" + current_weapon, "")),
            "scopes": translate_name(results.get("scopes_" + current_weapon, "")),
            "stocks": translate_name(results.get("stocks_" + current_weapon, "")),
            "poses": translate_name(results.get("poses", "")),
            "bag": translate_name(results.get("bag", "none"))
        }
    
        results_json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "results.json")
        with open(results_json_path, 'w', encoding='utf-8') as f:
            json.dump(results_json, f, ensure_ascii=False)

        root.update()
        if is_recognizing:
            time.sleep(0.1)
        else:
            time.sleep(0.01)
