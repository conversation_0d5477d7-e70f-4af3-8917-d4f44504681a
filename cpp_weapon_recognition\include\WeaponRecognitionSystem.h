#pragma once

#include "Types.h"
#include "ConfigManager.h"
#include "TemplateManager.h"
#include "ScreenCaptureEngine.h"
#include "RecognitionEngine.h"
#include "HotkeyManager.h"
#include "MouseListener.h"
#include "ConfigWriter.h"
#include "ThreadPool.h"

#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>

namespace WeaponRecognition {

class WeaponRecognitionSystem {
public:
    WeaponRecognitionSystem();
    ~WeaponRecognitionSystem();

    // 初始化系统
    bool initialize();
    
    // 运行系统
    void run();
    
    // 停止系统
    void stop();
    
    // 获取性能统计
    const PerformanceStats& getPerformanceStats() const { return performance_stats_; }

private:
    // 核心组件
    std::unique_ptr<ConfigManager> config_manager_;
    std::unique_ptr<TemplateManager> template_manager_;
    std::unique_ptr<ScreenCaptureEngine> capture_engine_;
    std::unique_ptr<RecognitionEngine> recognition_engine_;
    std::unique_ptr<HotkeyManager> hotkey_manager_;
    std::unique_ptr<MouseListener> mouse_listener_;
    std::unique_ptr<ConfigWriter> config_writer_;
    std::unique_ptr<ThreadPool> thread_pool_;

    // 系统状态
    std::atomic<bool> is_running_{false};
    std::atomic<bool> is_initialized_{false};
    std::atomic<WeaponType> current_weapon_type_{WeaponType::RIFLE};
    std::atomic<bool> right_button_pressed_{false};
    
    // 武器配置
    WeaponConfig rifle_config_;
    WeaponConfig sniper_config_;
    std::mutex config_mutex_;
    
    // 缩放控制
    std::atomic<double> scope_zoom_{1.6};
    
    // 性能统计
    PerformanceStats performance_stats_;
    std::mutex stats_mutex_;
    
    // 回调函数
    void onHotkeyPressed(HotkeyType type);
    void onMouseEvent(const MouseEvent& event);
    
    // 武器切换
    void switchWeapon(WeaponType type);
    void fastSwitchWeapon(WeaponType type);

    // 识别功能
    void triggerRecognition();
    void quickRecognizeWeapon(WeaponType type);
    void updateRealtimeStatus();

    // 缩放控制
    void handleScopeZoom(int delta);
    void handleWeaponChange();
    
    // 配置管理
    WeaponConfig& getCurrentConfig();
    const WeaponConfig& getCurrentConfig() const;
    void writeCurrentConfig();
    
    // 性能监控
    void updatePerformanceStats(double recognition_time, bool success);
    
    // 日志输出
    void logInfo(const std::string& message);
    void logError(const std::string& message);
    void logPerformance(const std::string& operation, double time_ms);
    
    // 初始化各个组件
    bool initializeComponents();
    bool loadConfiguration();
    bool loadTemplates();
    bool setupHotkeys();
    bool setupMouseListener();
    
    // 清理资源
    void cleanup();
};

} // namespace WeaponRecognition
