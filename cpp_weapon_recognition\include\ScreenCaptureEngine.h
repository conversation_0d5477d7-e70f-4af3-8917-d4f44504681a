#pragma once

#include "Types.h"
#include <opencv2/opencv.hpp>
#include <windows.h>
#include <memory>
#include <mutex>

namespace WeaponRecognition {

class ScreenCaptureEngine {
public:
    ScreenCaptureEngine();
    ~ScreenCaptureEngine();

    // 初始化截图引擎
    bool initialize();
    
    // 截取指定区域
    cv::Mat captureRegion(const Region& region);
    
    // 批量截取多个区域（并行优化）
    std::vector<cv::Mat> captureRegions(const std::vector<Region>& regions);
    
    // 获取屏幕分辨率
    std::pair<int, int> getScreenResolution() const;
    
    // 性能优化选项
    void setOptimizationLevel(int level); // 0=质量优先, 1=平衡, 2=速度优先
    
    // 获取截图性能统计
    double getAverageCaptureTime() const { return avg_capture_time_; }

private:
    // Windows GDI 相关
    HDC screen_dc_;
    HDC memory_dc_;
    HBITMAP bitmap_;
    BITMAPINFO bitmap_info_;
    
    // 屏幕信息
    int screen_width_;
    int screen_height_;
    
    // 性能优化
    int optimization_level_;
    std::unique_ptr<uint8_t[]> buffer_;
    size_t buffer_size_;
    
    // 性能统计
    mutable std::mutex stats_mutex_;
    double avg_capture_time_;
    int capture_count_;
    
    // 内部方法
    bool initializeGDI();
    void cleanupGDI();
    cv::Mat convertToMat(const Region& region);
    void updateCaptureStats(double time_ms);
    
    // 优化方法
    cv::Mat captureRegionFast(const Region& region);      // 速度优先
    cv::Mat captureRegionQuality(const Region& region);   // 质量优先
    cv::Mat captureRegionBalanced(const Region& region);  // 平衡模式
};

} // namespace WeaponRecognition
