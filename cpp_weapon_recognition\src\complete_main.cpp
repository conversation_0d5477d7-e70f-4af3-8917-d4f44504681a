#include <iostream>
#include <windows.h>
#include <opencv2/opencv.hpp>
#include <unordered_map>
#include <string>
#include <fstream>
#include <filesystem>
#include <thread>
#include <chrono>
#include <future>
#include <iomanip>
#include <sstream>

// DXGI includes
#include <d3d11.h>
#include <dxgi1_2.h>
#include <wrl/client.h>

#pragma comment(lib, "d3d11.lib")
#pragma comment(lib, "dxgi.lib")

using Microsoft::WRL::ComPtr;

// DXGI Screen Capture Class
class DXGIScreenCapture {
private:
    ComPtr<ID3D11Device> d3d_device;
    ComPtr<ID3D11DeviceContext> d3d_context;
    ComPtr<IDXGIOutputDuplication> desktop_duplication;
    ComPtr<ID3D11Texture2D> staging_texture;
    bool initialized = false;
    int screen_width = 0;
    int screen_height = 0;

public:
    bool Initialize();
    void Cleanup();
    cv::Mat CaptureRegion(const cv::Rect& region);
    bool IsInitialized() const { return initialized; }
    ~DXGIScreenCapture() { Cleanup(); }
};

// Global DXGI capture instance
DXGIScreenCapture g_dxgi_capture;

// Global debug flag
bool g_debug_mode = false;

// Simplified weapon configuration structure
struct WeaponConfig {
    std::string weapon_name;
    std::string muzzles;
    std::string grips;
    std::string scopes;
    std::string stocks;
    std::string poses = "None";
    double scope_zoom = 1.0;
    std::string car = "None";

    void reset() {
        weapon_name.clear();
        muzzles.clear();
        grips.clear();
        scopes.clear();
        stocks.clear();
        poses = "None";
        scope_zoom = 1.0;
        car = "None";
    }

    bool isEmpty() const {
        return weapon_name.empty();
    }
};

// Global variables
WeaponConfig rifle_config;
WeaponConfig sniper_config;
int current_weapon_slot = 1;
double scope_zoom = 1.6;
bool right_button_pressed = false;
bool is_running = true;
bool in_bag_interface = false;
bool tab_recognition_done = false;  // Track if recognition was done after tab
std::unordered_map<std::string, double> saved_zoom_values;  // Save zoom for each weapon

// Pose recognition variables
std::string current_pose = "none";  // Possible values: none/stand/down/crawl
std::string current_car_status = "none";  // Possible values: none/car
std::thread pose_recognition_thread;
bool pose_recognition_active = false;

// Timed weapon config writing (500ms interval)
std::thread config_write_thread;
bool config_write_active = false;

HHOOK keyboard_hook = nullptr;
HHOOK mouse_hook = nullptr;

// Region definitions (will be loaded from config.json)
std::unordered_map<std::string, cv::Rect> regions;

// Scope zoom configuration structure
struct ScopeZoomConfig {
    double min = 1.0;
    double max = 1.6;
    double step = 0.1;
};

// Scope zoom values (will be loaded from config.json)
std::unordered_map<std::string, ScopeZoomConfig> scope_zoom_configs;

// Template matching threshold (will be loaded from config.json)
double template_matching_threshold = 0.3;  // Default value

// Force GDI capture mode (will be loaded from config.json)
bool force_gdi_capture = false;  // Default value

// Save last successful attachment recognition results
struct LastSuccessfulAttachments {
    std::string muzzles;
    std::string grips;
    std::string scopes;
    std::string stocks;

    // Initialize as empty
    LastSuccessfulAttachments() : muzzles(""), grips(""), scopes(""), stocks("") {}
};

LastSuccessfulAttachments last_rifle_attachments;
LastSuccessfulAttachments last_sniper_attachments;

// Template storage
std::unordered_map<std::string, std::unordered_map<std::string, cv::Mat>> templates;

// Function to copy file permissions
bool copyFilePermissions(const std::string& source_path, const std::string& target_path) {
    // Get security descriptor from source file
    PSECURITY_DESCRIPTOR pSD = nullptr;
    DWORD dwSize = 0;

    // First call to get required buffer size
    GetFileSecurityA(source_path.c_str(),
                     DACL_SECURITY_INFORMATION | OWNER_SECURITY_INFORMATION | GROUP_SECURITY_INFORMATION,
                     nullptr, 0, &dwSize);

    if (dwSize == 0) {
        std::cout << "[PERMISSION] Failed to get security descriptor size for: " << source_path << std::endl;
        return false;
    }

    // Allocate buffer and get security descriptor
    pSD = (PSECURITY_DESCRIPTOR)LocalAlloc(LPTR, dwSize);
    if (!pSD) {
        std::cout << "[PERMISSION] Failed to allocate memory for security descriptor" << std::endl;
        return false;
    }

    if (!GetFileSecurityA(source_path.c_str(),
                          DACL_SECURITY_INFORMATION | OWNER_SECURITY_INFORMATION | GROUP_SECURITY_INFORMATION,
                          pSD, dwSize, &dwSize)) {
        std::cout << "[PERMISSION] Failed to get security descriptor from: " << source_path << std::endl;
        LocalFree(pSD);
        return false;
    }

    // Apply security descriptor to target file
    if (!SetFileSecurityA(target_path.c_str(),
                          DACL_SECURITY_INFORMATION | OWNER_SECURITY_INFORMATION | GROUP_SECURITY_INFORMATION,
                          pSD)) {
        std::cout << "[PERMISSION] Failed to set security descriptor to: " << target_path << std::endl;
        LocalFree(pSD);
        return false;
    }

    LocalFree(pSD);
    std::cout << "[PERMISSION] Successfully copied permissions from " << source_path << " to " << target_path << std::endl;
    return true;
}

// Function to create initial weapon.lua with proper permissions
bool createInitialWeaponFile(const std::string& file_path) {
    // Create the file first
    std::ofstream initial_file(file_path);
    if (!initial_file.is_open()) {
        std::cout << "[PERMISSION] Failed to create initial file: " << file_path << std::endl;
        return false;
    }

    // Write minimal content
    initial_file << "weapon_name = \"none\"\n";
    initial_file << "muzzles = \"none\"\n";
    initial_file << "grips = \"none\"\n";
    initial_file << "scopes = \"none\"\n";
    initial_file << "stocks = \"none\"\n";
    initial_file << "poses = \"none\"\n";
    initial_file << "scope_zoom = \"1.0\"\n";
    initial_file << "bag = \"none\"\n";
    initial_file << "car = \"none\"\n";
    initial_file << "shoot = \"None\"\n";
    initial_file << "cursor_x = 0\n";
    initial_file.close();

    // Set file attributes to normal
    SetFileAttributesA(file_path.c_str(), FILE_ATTRIBUTE_NORMAL);

    std::cout << "[PERMISSION] Created initial weapon file with proper permissions: " << file_path << std::endl;
    return true;
}

// Function to generate weapon config content as string
std::string generateConfigContent(const WeaponConfig& config) {
    std::ostringstream content;
    content << "weapon_name = \"" << config.weapon_name << "\"\n";
    content << "muzzles = \"" << config.muzzles << "\"\n";
    content << "muzzle = \"shouquan\"\n";
    content << "grips = \"" << config.grips << "\"\n";
    content << "scopes = \"" << config.scopes << "\"\n";
    content << "stocks = \"" << config.stocks << "\"\n";
    content << "poses = \"" << current_pose << "\"\n";
    content << "scope_zoom = \"" << std::fixed << std::setprecision(2) << config.scope_zoom << "\"\n";
    content << "bag = \"none\"\n";
    content << "car = \"" << current_car_status << "\"\n";
    content << "shoot = \"None\"\n";
    content << "cursor_x = 0\n";
    return content.str();
}

// Function to read current file content
std::string readCurrentFileContent(const std::string& file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        return "";
    }

    std::ostringstream content;
    content << file.rdbuf();
    file.close();
    return content.str();
}

// Program cleanup function
void cleanupOnExit() {
    std::cout << "Program shutting down, waiting for threads to finish..." << std::endl;

    // Set exit flags to stop all threads
    is_running = false;
    pose_recognition_active = false;
    config_write_active = false;

    // Give threads some time to finish naturally (max 1 second)
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    std::cout << "Cleanup completed" << std::endl;
}

// Dynamic resolution-based config loader
bool loadConfig(int screen_width, int screen_height) {
    // Get current working directory for diagnostics
    std::string current_dir = std::filesystem::current_path().string();
    std::cout << "[配置信息] 当前工作目录: " << current_dir << std::endl;
    std::cout << "[配置信息] 检测到屏幕分辨率: " << screen_width << "x" << screen_height << std::endl;

    // Check if nc directory exists
    if (!std::filesystem::exists("nc")) {
        std::cout << "[配置错误] 当前目录中未找到nc目录！" << std::endl;
        std::cout << "[配置错误] 请确保nc文件夹与可执行文件在同一目录中" << std::endl;
        return false;
    }

    // Try to load config for current resolution first
    std::string resolution_str = std::to_string(screen_width) + "x" + std::to_string(screen_height);
    std::string config_path = "nc/" + resolution_str + "/config.json";
    std::cout << "[配置信息] 查找配置文件: " << config_path << std::endl;
    std::ifstream file(config_path);

    // If current resolution config doesn't exist, try fallback resolutions
    std::vector<std::string> fallback_resolutions = {
        "1920x1080",  // Most common
        "2560x1440",  // Common high-res
        "1728x1080",  // Alternative
        "2304x1440",  // Alternative
        "2560x1600",  // Alternative
        "3840x2160"   // 4K
    };

    if (!file.is_open()) {
        std::cout << "[配置警告] 未找到 " << resolution_str << " 的配置，尝试备用配置..." << std::endl;

        // List available resolutions
        std::cout << "[配置信息] nc目录中可用的分辨率:" << std::endl;
        for (const auto& entry : std::filesystem::directory_iterator("nc")) {
            if (entry.is_directory()) {
                std::string dir_name = entry.path().filename().string();
                std::string test_config = "nc/" + dir_name + "/config.json";
                bool has_config = std::filesystem::exists(test_config);
                std::cout << "[配置信息]   - " << dir_name << (has_config ? " (有config.json)" : " (无config.json)") << std::endl;
            }
        }

        for (const auto& fallback : fallback_resolutions) {
            if (fallback == resolution_str) continue; // Skip if same as current

            config_path = "nc/" + fallback + "/config.json";
            std::cout << "[配置信息] 尝试备用配置: " << config_path << std::endl;
            file.open(config_path);
            if (file.is_open()) {
                std::cout << "[配置信息] 使用备用配置: " << config_path << std::endl;
                break;
            }
        }
    } else {
        std::cout << "[配置信息] 使用原生配置: " << config_path << std::endl;
    }

    if (!file.is_open()) {
        std::cout << "警告: 无法打开任何配置文件，使用硬编码区域" << std::endl;
        // Fallback to hardcoded regions (1920x1080 coordinates)
        regions = {
            {"poses", cv::Rect(711, 976, 30, 50)},
            {"weapon_name_rifle", cv::Rect(1371, 95, 70, 30)},
            {"weapon_name_sniper", cv::Rect(1371, 321, 70, 30)},
            {"muzzles_rifle", cv::Rect(1356, 242, 20, 20)},
            {"muzzles_sniper", cv::Rect(1356, 468, 20, 20)},
            {"grips_rifle", cv::Rect(1448, 253, 20, 20)},
            {"grips_sniper", cv::Rect(1448, 479, 20, 20)},
            {"scopes_rifle", cv::Rect(1631, 121, 20, 20)},
            {"scopes_sniper", cv::Rect(1631, 347, 20, 20)},
            {"stocks_rifle", cv::Rect(1765, 255, 20, 20)},
            {"stocks_sniper", cv::Rect(1765, 481, 20, 20)},
            {"car", cv::Rect(551, 1023, 13, 18)},
            {"bag", cv::Rect(672, 888, 35, 39)}
        };
        return false;
    }

    std::string line;
    std::string content;
    while (std::getline(file, line)) {
        content += line;
    }
    file.close();

    // Simple parsing for regions (not a full JSON parser)
    size_t regions_pos = content.find("\"regions\"");
    if (regions_pos == std::string::npos) {
        std::cout << "Warning: No regions found in config.json" << std::endl;
        return false;
    }

    // Parse each region from JSON content
    std::vector<std::string> region_names = {
        "poses", "weapon_name_rifle", "weapon_name_sniper",
        "muzzles_rifle", "muzzles_sniper", "grips_rifle", "grips_sniper",
        "scopes_rifle", "scopes_sniper", "stocks_rifle", "stocks_sniper",
        "bag", "car", "cursor"
    };

    for (const std::string& region_name : region_names) {
        size_t region_pos = content.find("\"" + region_name + "\"");
        if (region_pos != std::string::npos) {
            // Find the array start
            size_t array_start = content.find("[", region_pos);
            size_t array_end = content.find("]", array_start);
            if (array_start != std::string::npos && array_end != std::string::npos) {
                std::string array_content = content.substr(array_start + 1, array_end - array_start - 1);

                // Parse the four numbers
                std::vector<int> coords;
                std::stringstream ss(array_content);
                std::string item;
                while (std::getline(ss, item, ',')) {
                    // Remove whitespace
                    item.erase(std::remove_if(item.begin(), item.end(), ::isspace), item.end());
                    if (!item.empty()) {
                        coords.push_back(std::stoi(item));
                    }
                }

                if (coords.size() == 4) {
                    regions[region_name] = cv::Rect(coords[0], coords[1], coords[2], coords[3]);
                    if (g_debug_mode) {
                        std::cout << "[CONFIG] Loaded " << region_name << ": [" << coords[0] << ", " << coords[1] << ", " << coords[2] << ", " << coords[3] << "]" << std::endl;
                    }
                }
            }
        }
    }

    // Parse scope zoom configurations from JSON
    size_t scope_zoom_pos = content.find("\"scope_zoom\"");
    if (scope_zoom_pos != std::string::npos) {
        // Parse x6 configuration
        size_t x6_pos = content.find("\"x6\"", scope_zoom_pos);
        if (x6_pos != std::string::npos) {
            ScopeZoomConfig x6_config;

            // Find min value
            size_t min_pos = content.find("\"min\"", x6_pos);
            if (min_pos != std::string::npos) {
                size_t colon_pos = content.find(":", min_pos);
                size_t value_start = content.find_first_not_of(" \t", colon_pos + 1);
                size_t value_end = content.find_first_of(",}", value_start);
                if (value_start != std::string::npos && value_end != std::string::npos) {
                    x6_config.min = std::stod(content.substr(value_start, value_end - value_start));
                }
            }

            // Find max value
            size_t max_pos = content.find("\"max\"", x6_pos);
            if (max_pos != std::string::npos) {
                size_t colon_pos = content.find(":", max_pos);
                size_t value_start = content.find_first_not_of(" \t", colon_pos + 1);
                size_t value_end = content.find_first_of(",}", value_start);
                if (value_start != std::string::npos && value_end != std::string::npos) {
                    x6_config.max = std::stod(content.substr(value_start, value_end - value_start));
                }
            }

            // Find step value
            size_t step_pos = content.find("\"step\"", x6_pos);
            if (step_pos != std::string::npos) {
                size_t colon_pos = content.find(":", step_pos);
                size_t value_start = content.find_first_not_of(" \t", colon_pos + 1);
                size_t value_end = content.find_first_of(",}", value_start);
                if (value_start != std::string::npos && value_end != std::string::npos) {
                    x6_config.step = std::stod(content.substr(value_start, value_end - value_start));
                }
            }

            scope_zoom_configs["x6"] = x6_config;
        }

        // Parse x8 configuration
        size_t x8_pos = content.find("\"x8\"", scope_zoom_pos);
        if (x8_pos != std::string::npos) {
            ScopeZoomConfig x8_config;

            // Find min value
            size_t min_pos = content.find("\"min\"", x8_pos);
            if (min_pos != std::string::npos) {
                size_t colon_pos = content.find(":", min_pos);
                size_t value_start = content.find_first_not_of(" \t", colon_pos + 1);
                size_t value_end = content.find_first_of(",}", value_start);
                if (value_start != std::string::npos && value_end != std::string::npos) {
                    x8_config.min = std::stod(content.substr(value_start, value_end - value_start));
                }
            }

            // Find max value
            size_t max_pos = content.find("\"max\"", x8_pos);
            if (max_pos != std::string::npos) {
                size_t colon_pos = content.find(":", max_pos);
                size_t value_start = content.find_first_not_of(" \t", colon_pos + 1);
                size_t value_end = content.find_first_of(",}", value_start);
                if (value_start != std::string::npos && value_end != std::string::npos) {
                    x8_config.max = std::stod(content.substr(value_start, value_end - value_start));
                }
            }

            // Find step value
            size_t step_pos = content.find("\"step\"", x8_pos);
            if (step_pos != std::string::npos) {
                size_t colon_pos = content.find(":", step_pos);
                size_t value_start = content.find_first_not_of(" \t", colon_pos + 1);
                size_t value_end = content.find_first_of(",}", value_start);
                if (value_start != std::string::npos && value_end != std::string::npos) {
                    x8_config.step = std::stod(content.substr(value_start, value_end - value_start));
                }
            }

            scope_zoom_configs["x8"] = x8_config;
        }

        std::cout << "已加载瞄镜缩放配置:" << std::endl;
        std::cout << "  x6: 最小=" << scope_zoom_configs["x6"].min << ", 最大=" << scope_zoom_configs["x6"].max << ", 步长=" << scope_zoom_configs["x6"].step << std::endl;
        std::cout << "  x8: 最小=" << scope_zoom_configs["x8"].min << ", 最大=" << scope_zoom_configs["x8"].max << ", 步长=" << scope_zoom_configs["x8"].step << std::endl;
    } else {
        // Default configurations if not found in config
        scope_zoom_configs["x6"] = {1.0, 1.6, 0.1};
        scope_zoom_configs["x8"] = {1.0, 2.0, 0.1};
        std::cout << "使用默认瞄镜缩放配置: x6(1.0-1.6, 0.1), x8(1.0-2.0, 0.1)" << std::endl;
    }

    // Load template matching threshold
    size_t threshold_pos = content.find("\"template_matching_threshold\"");
    if (threshold_pos != std::string::npos) {
        size_t colon_pos = content.find(":", threshold_pos);
        size_t value_start = content.find_first_not_of(" \t", colon_pos + 1);
        size_t value_end = content.find_first_of(",}", value_start);
        if (value_start != std::string::npos && value_end != std::string::npos) {
            template_matching_threshold = std::stod(content.substr(value_start, value_end - value_start));
            std::cout << "已加载模板匹配阈值: " << template_matching_threshold << std::endl;
        }
    } else {
        std::cout << "使用默认模板匹配阈值: " << template_matching_threshold << std::endl;
    }

    // Load force GDI capture setting
    size_t gdi_pos = content.find("\"force_gdi_capture\"");
    if (gdi_pos != std::string::npos) {
        size_t colon_pos = content.find(":", gdi_pos);
        size_t value_start = content.find_first_not_of(" \t", colon_pos + 1);
        size_t value_end = content.find_first_of(",}", value_start);
        if (value_start != std::string::npos && value_end != std::string::npos) {
            std::string value = content.substr(value_start, value_end - value_start);
            force_gdi_capture = (value == "true");
            std::cout << "已加载强制GDI捕获设置: " << (force_gdi_capture ? "true" : "false") << std::endl;
        }
    } else {
        std::cout << "使用默认强制GDI捕获设置: " << (force_gdi_capture ? "true" : "false") << std::endl;
    }

    std::cout << "已从config.json加载区域配置" << std::endl;
    return true;
}

// DXGI Screen Capture Implementation
bool DXGIScreenCapture::Initialize() {
    HRESULT hr;

    // Create D3D11 device
    D3D_FEATURE_LEVEL feature_levels[] = {
        D3D_FEATURE_LEVEL_11_1,
        D3D_FEATURE_LEVEL_11_0,
        D3D_FEATURE_LEVEL_10_1,
        D3D_FEATURE_LEVEL_10_0
    };

    hr = D3D11CreateDevice(
        nullptr,                    // Adapter
        D3D_DRIVER_TYPE_HARDWARE,   // Driver Type
        nullptr,                    // Software
        0,                          // Flags
        feature_levels,             // Feature Levels
        ARRAYSIZE(feature_levels),  // Num Feature Levels
        D3D11_SDK_VERSION,          // SDK Version
        &d3d_device,                // Device
        nullptr,                    // Feature Level
        &d3d_context                // Device Context
    );

    if (FAILED(hr)) {
        std::cout << "[DXGI ERROR] Failed to create D3D11 device" << std::endl;
        std::cout << "[DXGI ERROR] Error code: 0x" << std::hex << hr << std::dec << std::endl;
        if (hr == DXGI_ERROR_UNSUPPORTED) {
            std::cout << "[DXGI ERROR] Hardware does not support DirectX 11" << std::endl;
        } else if (hr == E_INVALIDARG) {
            std::cout << "[DXGI ERROR] Invalid arguments to D3D11CreateDevice" << std::endl;
        } else if (hr == E_OUTOFMEMORY) {
            std::cout << "[DXGI ERROR] Out of memory" << std::endl;
        }
        return false;
    }

    // Get DXGI device
    ComPtr<IDXGIDevice> dxgi_device;
    hr = d3d_device.As(&dxgi_device);
    if (FAILED(hr)) {
        std::cout << "[DXGI ERROR] Failed to get DXGI device: 0x" << std::hex << hr << std::dec << std::endl;
        return false;
    }

    // Get DXGI adapter
    ComPtr<IDXGIAdapter> dxgi_adapter;
    hr = dxgi_device->GetAdapter(&dxgi_adapter);
    if (FAILED(hr)) {
        std::cout << "[DXGI ERROR] Failed to get DXGI adapter: 0x" << std::hex << hr << std::dec << std::endl;
        return false;
    }

    // Get adapter description for diagnostics
    DXGI_ADAPTER_DESC adapter_desc;
    if (SUCCEEDED(dxgi_adapter->GetDesc(&adapter_desc))) {
        std::wcout << L"[DXGI INFO] Graphics adapter: " << adapter_desc.Description << std::endl;
        std::cout << "[DXGI INFO] Video memory: " << (adapter_desc.DedicatedVideoMemory / 1024 / 1024) << " MB" << std::endl;
    }

    // Get output (monitor)
    ComPtr<IDXGIOutput> dxgi_output;
    hr = dxgi_adapter->EnumOutputs(0, &dxgi_output);
    if (FAILED(hr)) {
        std::cout << "[DXGI ERROR] Failed to get DXGI output: 0x" << std::hex << hr << std::dec << std::endl;
        if (hr == DXGI_ERROR_NOT_FOUND) {
            std::cout << "[DXGI ERROR] No display outputs found" << std::endl;
        }
        return false;
    }

    // Get output1 interface
    ComPtr<IDXGIOutput1> dxgi_output1;
    hr = dxgi_output.As(&dxgi_output1);
    if (FAILED(hr)) {
        std::cout << "[DXGI ERROR] Failed to get DXGI output1: 0x" << std::hex << hr << std::dec << std::endl;
        std::cout << "[DXGI ERROR] This may indicate an old Windows version (need Windows 8+)" << std::endl;
        return false;
    }

    // Create desktop duplication
    hr = dxgi_output1->DuplicateOutput(d3d_device.Get(), &desktop_duplication);
    if (FAILED(hr)) {
        std::cout << "[DXGI ERROR] Failed to create desktop duplication: 0x" << std::hex << hr << std::dec << std::endl;
        if (hr == DXGI_ERROR_NOT_CURRENTLY_AVAILABLE) {
            std::cout << "[DXGI ERROR] Desktop duplication not available (may be in use by another app)" << std::endl;
        } else if (hr == DXGI_ERROR_UNSUPPORTED) {
            std::cout << "[DXGI ERROR] Desktop duplication not supported by this graphics adapter" << std::endl;
        } else if (hr == E_ACCESSDENIED) {
            std::cout << "[DXGI ERROR] Access denied (may need administrator privileges)" << std::endl;
        } else if (hr == DXGI_ERROR_SESSION_DISCONNECTED) {
            std::cout << "[DXGI ERROR] Session disconnected (remote desktop?)" << std::endl;
        }
        return false;
    }

    // Get output description
    DXGI_OUTPUT_DESC output_desc;
    dxgi_output->GetDesc(&output_desc);
    screen_width = output_desc.DesktopCoordinates.right - output_desc.DesktopCoordinates.left;
    screen_height = output_desc.DesktopCoordinates.bottom - output_desc.DesktopCoordinates.top;

    std::cout << "DXGI initialized successfully. Screen: " << screen_width << "x" << screen_height << std::endl;
    initialized = true;
    return true;
}

void DXGIScreenCapture::Cleanup() {
    if (staging_texture) {
        staging_texture.Reset();
    }
    if (desktop_duplication) {
        desktop_duplication.Reset();
    }
    if (d3d_context) {
        d3d_context.Reset();
    }
    if (d3d_device) {
        d3d_device.Reset();
    }
    initialized = false;
    std::cout << "DXGI cleanup completed" << std::endl;
}

cv::Mat DXGIScreenCapture::CaptureRegion(const cv::Rect& region) {
    if (!initialized) {
        std::cout << "DXGI not initialized" << std::endl;
        return cv::Mat();
    }

    HRESULT hr;
    DXGI_OUTDUPL_FRAME_INFO frame_info;
    ComPtr<IDXGIResource> desktop_resource;

    // Acquire next frame
    hr = desktop_duplication->AcquireNextFrame(100, &frame_info, &desktop_resource);
    if (FAILED(hr)) {
        if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
            // No new frame, try to use previous frame
            // For now, return empty mat
            return cv::Mat();
        }
        std::cout << "Failed to acquire frame: " << std::hex << hr << std::endl;
        return cv::Mat();
    }

    // Get desktop texture
    ComPtr<ID3D11Texture2D> desktop_texture;
    hr = desktop_resource.As(&desktop_texture);
    if (FAILED(hr)) {
        desktop_duplication->ReleaseFrame();
        std::cout << "Failed to get desktop texture: " << std::hex << hr << std::endl;
        return cv::Mat();
    }

    // Create staging texture if not exists
    if (!staging_texture) {
        D3D11_TEXTURE2D_DESC texture_desc;
        desktop_texture->GetDesc(&texture_desc);
        texture_desc.Usage = D3D11_USAGE_STAGING;
        texture_desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
        texture_desc.BindFlags = 0;
        texture_desc.MiscFlags = 0;

        hr = d3d_device->CreateTexture2D(&texture_desc, nullptr, &staging_texture);
        if (FAILED(hr)) {
            desktop_duplication->ReleaseFrame();
            std::cout << "Failed to create staging texture: " << std::hex << hr << std::endl;
            return cv::Mat();
        }
    }

    // Copy desktop to staging texture
    d3d_context->CopyResource(staging_texture.Get(), desktop_texture.Get());

    // Map staging texture
    D3D11_MAPPED_SUBRESOURCE mapped_resource;
    hr = d3d_context->Map(staging_texture.Get(), 0, D3D11_MAP_READ, 0, &mapped_resource);
    if (FAILED(hr)) {
        desktop_duplication->ReleaseFrame();
        std::cout << "Failed to map staging texture: " << std::hex << hr << std::endl;
        return cv::Mat();
    }

    // Extract region from mapped data
    cv::Mat result(region.height, region.width, CV_8UC4);

    for (int y = 0; y < region.height; ++y) {
        if (region.y + y >= screen_height) break;

        const uint8_t* src_row = static_cast<const uint8_t*>(mapped_resource.pData) +
                                (region.y + y) * mapped_resource.RowPitch +
                                region.x * 4;
        uint8_t* dst_row = result.ptr<uint8_t>(y);

        int copy_width = std::min(region.width, screen_width - region.x);
        memcpy(dst_row, src_row, copy_width * 4);
    }

    // Unmap and release
    d3d_context->Unmap(staging_texture.Get(), 0);
    desktop_duplication->ReleaseFrame();

    // Convert BGRA to BGR
    cv::Mat bgr_result;
    cv::cvtColor(result, bgr_result, cv::COLOR_BGRA2BGR);

    return bgr_result;
}

// Screen capture function using DXGI
cv::Mat captureRegion(const cv::Rect& region) {
    // Try DXGI first
    if (g_dxgi_capture.IsInitialized()) {
        cv::Mat result = g_dxgi_capture.CaptureRegion(region);
        if (!result.empty()) {
            return result;
        }
        std::cout << "DXGI capture failed, falling back to GDI" << std::endl;
    }

    // Fallback to GDI if DXGI fails
    HDC screen_dc = GetDC(nullptr);
    HDC memory_dc = CreateCompatibleDC(screen_dc);

    BITMAPINFO bitmap_info;
    ZeroMemory(&bitmap_info, sizeof(BITMAPINFO));
    bitmap_info.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bitmap_info.bmiHeader.biWidth = region.width;
    bitmap_info.bmiHeader.biHeight = -region.height;
    bitmap_info.bmiHeader.biPlanes = 1;
    bitmap_info.bmiHeader.biBitCount = 32;
    bitmap_info.bmiHeader.biCompression = BI_RGB;

    void* bits = nullptr;
    HBITMAP bitmap = CreateDIBSection(memory_dc, &bitmap_info, DIB_RGB_COLORS, &bits, nullptr, 0);

    if (!bitmap || !bits) {
        ReleaseDC(nullptr, screen_dc);
        DeleteDC(memory_dc);
        return cv::Mat();
    }

    HBITMAP old_bitmap = (HBITMAP)SelectObject(memory_dc, bitmap);

    if (!BitBlt(memory_dc, 0, 0, region.width, region.height, screen_dc, region.x, region.y, SRCCOPY)) {
        SelectObject(memory_dc, old_bitmap);
        DeleteObject(bitmap);
        ReleaseDC(nullptr, screen_dc);
        DeleteDC(memory_dc);
        return cv::Mat();
    }

    cv::Mat bgra_image(region.height, region.width, CV_8UC4, bits);
    cv::Mat result;
    cv::cvtColor(bgra_image, result, cv::COLOR_BGRA2BGR);

    SelectObject(memory_dc, old_bitmap);
    DeleteObject(bitmap);
    ReleaseDC(nullptr, screen_dc);
    DeleteDC(memory_dc);

    return result;
}

// Load templates with dynamic resolution support
bool loadTemplates(int screen_width, int screen_height) {
    std::vector<std::string> categories = {"weapons", "muzzles", "grips", "scopes", "stocks", "poses", "car", "bag"};

    // Try to find templates for current resolution first
    std::string resolution_str = std::to_string(screen_width) + "x" + std::to_string(screen_height);
    std::string base_template_path = "nc/" + resolution_str + "/weapon_templates/";

    std::cout << "[TEMPLATE INFO] Looking for templates: " << base_template_path << std::endl;

    // Fallback resolutions if current resolution templates don't exist
    std::vector<std::string> fallback_resolutions = {
        "1920x1080",  // Most common
        "2560x1440",  // Common high-res
        "1728x1080",  // Alternative
        "2304x1440",  // Alternative
        "2560x1600",  // Alternative
        "3840x2160"   // 4K
    };

    // Check if current resolution templates exist
    if (!std::filesystem::exists(base_template_path)) {
        std::cout << "[TEMPLATE WARNING] Templates for " << resolution_str << " not found, trying fallbacks..." << std::endl;

        // List available template directories
        std::cout << "[TEMPLATE INFO] Available template directories:" << std::endl;
        for (const auto& entry : std::filesystem::directory_iterator("nc")) {
            if (entry.is_directory()) {
                std::string dir_name = entry.path().filename().string();
                std::string template_dir = "nc/" + dir_name + "/weapon_templates/";
                bool has_templates = std::filesystem::exists(template_dir);
                std::cout << "[TEMPLATE INFO]   - " << dir_name << (has_templates ? " (has weapon_templates)" : " (no weapon_templates)") << std::endl;
            }
        }

        for (const auto& fallback : fallback_resolutions) {
            if (fallback == resolution_str) continue; // Skip if same as current

            base_template_path = "nc/" + fallback + "/weapon_templates/";
            if (std::filesystem::exists(base_template_path)) {
                std::cout << "Using fallback templates: " << base_template_path << std::endl;
                break;
            }
        }
    } else {
        std::cout << "Using native templates: " << base_template_path << std::endl;
    }

    for (const auto& category : categories) {
        std::string category_path = base_template_path + category;

        if (!std::filesystem::exists(category_path)) {
            std::cout << "Warning: Template directory not found: " << category_path << std::endl;
            continue;
        }

        templates[category] = std::unordered_map<std::string, cv::Mat>();

        for (const auto& entry : std::filesystem::directory_iterator(category_path)) {
            if (entry.is_regular_file()) {
                std::string file_path = entry.path().string();
                std::string extension = entry.path().extension().string();

                if (extension == ".png" || extension == ".jpg" || extension == ".jpeg") {
                    std::string template_name = entry.path().stem().string();
                    cv::Mat template_img = cv::imread(file_path, cv::IMREAD_GRAYSCALE);

                    if (!template_img.empty()) {
                        templates[category][template_name] = template_img;
                    }
                }
            }
        }

        std::cout << "Loaded " << category << ": " << templates[category].size() << " templates" << std::endl;
    }

    return true;
}

// Template matching with debug
std::string recognizeRegion(const cv::Mat& image, const std::string& category, bool debug = false) {
    if (image.empty() || templates[category].empty()) {
        if (debug) std::cout << "  [DEBUG] Empty image or no templates for " << category << std::endl;
        return "";
    }

    cv::Mat gray_image;
    if (image.channels() == 3) {
        cv::cvtColor(image, gray_image, cv::COLOR_BGR2GRAY);
    } else if (image.channels() == 4) {
        cv::cvtColor(image, gray_image, cv::COLOR_BGRA2GRAY);
    } else {
        gray_image = image.clone();
    }

    // Save debug images
    if (debug) {
        std::string debug_path_original = "C:\\Temp\\debug_" + category + "_original.png";
        std::string debug_path_gray = "C:\\Temp\\debug_" + category + "_gray.png";
        cv::imwrite(debug_path_original, image);
        cv::imwrite(debug_path_gray, gray_image);
        std::cout << "  [DEBUG] Image size: " << image.cols << "x" << image.rows << " channels: " << image.channels() << std::endl;
        std::cout << "  [DEBUG] Saved original to: " << debug_path_original << std::endl;
        std::cout << "  [DEBUG] Saved gray to: " << debug_path_gray << std::endl;
    }

    std::string best_match;
    double best_confidence = 0.0;
    double threshold = template_matching_threshold; // Load from config.json

    for (const auto& [template_name, template_img] : templates[category]) {
        if (template_img.empty()) {
            if (debug) std::cout << "  [DEBUG] " << template_name << ": EMPTY TEMPLATE" << std::endl;
            continue;
        }

        // Check if template size is compatible with image
        if (template_img.rows > gray_image.rows || template_img.cols > gray_image.cols) {
            if (debug) std::cout << "  [DEBUG] " << template_name << ": TEMPLATE TOO LARGE ("
                                << template_img.cols << "x" << template_img.rows << " vs "
                                << gray_image.cols << "x" << gray_image.rows << ")" << std::endl;
            continue;
        }

        cv::Mat result;
        cv::matchTemplate(gray_image, template_img, result, cv::TM_CCOEFF_NORMED);

        double min_val, max_val;
        cv::Point min_loc, max_loc;
        cv::minMaxLoc(result, &min_val, &max_val, &min_loc, &max_loc);

        if (debug) {
            std::cout << "  [DEBUG] " << template_name << ": " << std::fixed << std::setprecision(3) << max_val
                      << " at (" << max_loc.x << "," << max_loc.y << ")" << std::endl;
        }

        if (max_val > best_confidence && max_val > threshold) {
            best_confidence = max_val;
            best_match = template_name;
        }
    }

    if (debug) {
        std::cout << "  [DEBUG] Best match: " << best_match << " (confidence: " << std::fixed << std::setprecision(3) << best_confidence << ")" << std::endl;
    }

    return best_match;
}

// Write configuration file
bool writeWeaponConfig(const WeaponConfig& config) {
    try {
        std::string final_path = "C:\\Temp\\weapon.lua";

        // Ensure directory exists
        std::filesystem::create_directories("C:\\Temp");

        // Generate new content
        std::string new_content = generateConfigContent(config);

        // Read current file content
        std::string current_content = readCurrentFileContent(final_path);

        // Compare content - only write if different
        if (new_content == current_content) {
            if (g_debug_mode) {
                std::cout << "[FILE INFO] Content unchanged, skipping write" << std::endl;
            }
            return true;
        }

        std::cout << "[FILE INFO] Content changed, writing new config" << std::endl;

        // Test write permissions
        std::string test_path = "C:\\Temp\\test_write.tmp";
        try {
            std::ofstream test_file(test_path);
            if (test_file.is_open()) {
                test_file << "Test write permission";
                test_file.close();
                std::filesystem::remove(test_path);
                std::cout << "[FILE INFO] Write permission test: Success" << std::endl;
            } else {
                std::cout << "[FILE ERROR] Cannot open test file for writing" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "[FILE ERROR] Write permission test failed: " << e.what() << std::endl;
        }

        // Direct write to final file (no temporary file)
        std::ofstream file(final_path);
        if (!file.is_open()) {
            std::cout << "[FILE ERROR] Cannot open file for writing: " << final_path << std::endl;
            return false;
        }

        // Write the new content directly
        file << new_content;
        file.close();

        std::cout << "[FILE WRITE] poses = \"" << current_pose << "\"" << std::endl;
        std::cout << "[FILE WRITE] car = \"" << current_car_status << "\"" << std::endl;
        // Verify file was written successfully
        if (!std::filesystem::exists(final_path)) {
            std::cout << "[FILE ERROR] File does not exist after write" << std::endl;
            return false;
        }

        // Test read access
        std::ifstream test_read(final_path);
        if (!test_read.is_open()) {
            std::cout << "[FILE ERROR] Cannot read file after write - permission issue" << std::endl;
            return false;
        }
        test_read.close();

        std::cout << "[FILE INFO] weapon.lua written successfully using direct write" << std::endl;

        return true;
    } catch (const std::exception& e) {
        std::cout << "[FILE ERROR] Failed to write config: " << e.what() << std::endl;
        return false;
    }
}

// Recognize weapon configuration
WeaponConfig recognizeWeaponConfig(bool is_rifle) {
    WeaponConfig config;

    // Get reference to last successful attachments
    LastSuccessfulAttachments& last_attachments = is_rifle ? last_rifle_attachments : last_sniper_attachments;

    std::string weapon_key = is_rifle ? "weapon_name_rifle" : "weapon_name_sniper";
    std::string muzzle_key = is_rifle ? "muzzles_rifle" : "muzzles_sniper";
    std::string grip_key = is_rifle ? "grips_rifle" : "grips_sniper";
    std::string scope_key = is_rifle ? "scopes_rifle" : "scopes_sniper";
    std::string stock_key = is_rifle ? "stocks_rifle" : "stocks_sniper";

    if (g_debug_mode) {
        std::cout << "[DEBUG] Recognizing " << (is_rifle ? "Rifle" : "Sniper") << " configuration..." << std::endl;
    }

    // Recognize each component
    if (regions.count(weapon_key)) {
        cv::Mat image = captureRegion(regions[weapon_key]);
        if (g_debug_mode) std::cout << "[DEBUG] Recognizing weapon..." << std::endl;

        // Create debug folder and save image for analysis (debug mode only)
        if (g_debug_mode) {
            std::string weapon_type = is_rifle ? "rifle" : "sniper";
            std::string debug_folder = "C:\\Temp\\debug_" + weapon_type;
            std::string mkdir_cmd = "if not exist \"" + debug_folder + "\" mkdir \"" + debug_folder + "\"";
            system(mkdir_cmd.c_str());

            std::string debug_path = debug_folder + "\\weapon.png";
            cv::imwrite(debug_path, image);
            std::cout << "[DEBUG] Saved weapon region to: " << debug_path << std::endl;
        }

        config.weapon_name = recognizeRegion(image, "weapons", g_debug_mode);
        if (g_debug_mode) {
            std::cout << "[DEBUG] Weapon recognition result: '" << config.weapon_name << "'" << std::endl;
        }
    } else {
        if (g_debug_mode) {
            std::cout << "[DEBUG] Weapon region '" << weapon_key << "' not found in config" << std::endl;
        }
    }

    if (regions.count(muzzle_key)) {
        cv::Mat image = captureRegion(regions[muzzle_key]);
        if (g_debug_mode) std::cout << "[DEBUG] Recognizing muzzle..." << std::endl;

        // Save debug image for analysis (debug mode only)
        if (g_debug_mode) {
            std::string weapon_type = is_rifle ? "rifle" : "sniper";
            std::string debug_folder = "C:\\Temp\\debug_" + weapon_type;
            std::string debug_path = debug_folder + "\\muzzle.png";
            cv::imwrite(debug_path, image);
            std::cout << "[DEBUG] Saved muzzle region to: " << debug_path << std::endl;
        }

        std::string muzzle_result = recognizeRegion(image, "muzzles", g_debug_mode);
        if (!muzzle_result.empty()) {
            // Recognition successful, update config and save as last successful result
            config.muzzles = muzzle_result;
            last_attachments.muzzles = muzzle_result;
        } else {
            // Recognition failed, use last successful result or default "none"
            config.muzzles = last_attachments.muzzles.empty() ? "none" : last_attachments.muzzles;
        }
        if (g_debug_mode) {
            std::cout << "[DEBUG] Muzzle recognition result: '" << config.muzzles << "'" << std::endl;
        }
    } else {
        if (g_debug_mode) {
            std::cout << "[DEBUG] Muzzle region '" << muzzle_key << "' not found in config" << std::endl;
        }
    }

    if (regions.count(grip_key)) {
        cv::Mat image = captureRegion(regions[grip_key]);
        if (g_debug_mode) std::cout << "[DEBUG] Recognizing grip..." << std::endl;

        // Save debug image for analysis (debug mode only)
        if (g_debug_mode) {
            std::string weapon_type = is_rifle ? "rifle" : "sniper";
            std::string debug_folder = "C:\\Temp\\debug_" + weapon_type;
            std::string debug_path = debug_folder + "\\grip.png";
            cv::imwrite(debug_path, image);
            std::cout << "[DEBUG] Saved grip region to: " << debug_path << std::endl;
        }

        std::string grip_result = recognizeRegion(image, "grips", g_debug_mode);
        if (!grip_result.empty()) {
            // Recognition successful, update config and save as last successful result
            config.grips = grip_result;
            last_attachments.grips = grip_result;
        } else {
            // Recognition failed, use last successful result or default "none"
            config.grips = last_attachments.grips.empty() ? "none" : last_attachments.grips;
        }
        if (g_debug_mode) {
            std::cout << "[DEBUG] Grip recognition result: '" << config.grips << "'" << std::endl;
        }
    } else {
        if (g_debug_mode) {
            std::cout << "[DEBUG] Grip region '" << grip_key << "' not found in config" << std::endl;
        }
    }

    if (regions.count(scope_key)) {
        cv::Mat image = captureRegion(regions[scope_key]);
        if (g_debug_mode) std::cout << "[DEBUG] Recognizing scope..." << std::endl;

        // Save debug image for analysis (debug mode only)
        if (g_debug_mode) {
            std::string weapon_type = is_rifle ? "rifle" : "sniper";
            std::string debug_folder = "C:\\Temp\\debug_" + weapon_type;
            std::string debug_path = debug_folder + "\\scope.png";
            cv::imwrite(debug_path, image);
            std::cout << "[DEBUG] Saved scope region to: " << debug_path << std::endl;
        }

        std::string scope_result = recognizeRegion(image, "scopes", g_debug_mode);
        if (!scope_result.empty()) {
            // Recognition successful, update config and save as last successful result
            config.scopes = scope_result;
            last_attachments.scopes = scope_result;
        } else {
            // Recognition failed, use last successful result or default "none"
            config.scopes = last_attachments.scopes.empty() ? "none" : last_attachments.scopes;
        }
        if (g_debug_mode) {
            std::cout << "[DEBUG] Scope recognition result: '" << config.scopes << "'" << std::endl;
        }

        // Set default zoom value
        if (config.scopes == "x6" || config.scopes == "x8") {
            config.scope_zoom = 1.6; // High magnification scope default max zoom
        } else {
            config.scope_zoom = 1.0; // Other scopes fixed at 1.0
        }
    }

    if (regions.count(stock_key)) {
        cv::Mat image = captureRegion(regions[stock_key]);
        if (g_debug_mode) std::cout << "[DEBUG] Recognizing stock..." << std::endl;

        // Save debug image for analysis (debug mode only)
        if (g_debug_mode) {
            std::string weapon_type = is_rifle ? "rifle" : "sniper";
            std::string debug_folder = "C:\\Temp\\debug_" + weapon_type;
            std::string debug_path = debug_folder + "\\stock.png";
            cv::imwrite(debug_path, image);
            std::cout << "[DEBUG] Saved stock region to: " << debug_path << std::endl;
        }

        std::string stock_result = recognizeRegion(image, "stocks", g_debug_mode);
        if (!stock_result.empty()) {
            // Recognition successful, update config and save as last successful result
            config.stocks = stock_result;
            last_attachments.stocks = stock_result;
        } else {
            // Recognition failed, use last successful result or default "none"
            config.stocks = last_attachments.stocks.empty() ? "none" : last_attachments.stocks;
        }
        if (g_debug_mode) {
            std::cout << "[DEBUG] Stock recognition result: '" << config.stocks << "'" << std::endl;
        }
    } else {
        if (g_debug_mode) {
            std::cout << "[DEBUG] Stock region '" << stock_key << "' not found in config" << std::endl;
        }
    }

    // Recognize pose
    if (regions.count("poses")) {
        cv::Mat image = captureRegion(regions["poses"]);
        if (g_debug_mode) std::cout << "[DEBUG] Recognizing pose..." << std::endl;
        std::string pose_result = recognizeRegion(image, "poses", g_debug_mode);
        config.poses = pose_result.empty() ? "None" : pose_result;
    }

    // Recognize vehicle status
    if (regions.count("car")) {
        cv::Mat image = captureRegion(regions["car"]);
        if (g_debug_mode) std::cout << "[DEBUG] Recognizing vehicle..." << std::endl;
        std::string car_result = recognizeRegion(image, "car", g_debug_mode);
        config.car = car_result.empty() ? "None" : "car";
    }

    return config;
}

// Check if bag interface is open
bool isBagInterfaceOpen() {
    if (!regions.count("bag")) {
        std::cout << "Warning: No bag region defined" << std::endl;
        return false;
    }

    cv::Mat bag_image = captureRegion(regions["bag"]);
    if (bag_image.empty()) {
        std::cout << "Failed to capture bag region" << std::endl;
        return false;
    }

    std::string bag_result = recognizeRegion(bag_image, "bag", true);
    bool bag_detected = !bag_result.empty();

    in_bag_interface = bag_detected;  // Update global state
    std::cout << "Bag interface check: " << (bag_detected ? "OPEN" : "CLOSED") << std::endl;
    return bag_detected;
}

// Save zoom value for a weapon
void saveZoomValue(const std::string& weapon_name, const std::string& scope, double zoom) {
    if (!weapon_name.empty() && (scope == "x6" || scope == "x8")) {
        std::string key = weapon_name + "_" + scope;
        saved_zoom_values[key] = zoom;
        std::cout << "Saved zoom " << zoom << " for " << weapon_name << " with " << scope << std::endl;
    }
}

// Get saved zoom value for a weapon
double getSavedZoomValue(const std::string& weapon_name, const std::string& scope) {
    if (!weapon_name.empty() && (scope == "x6" || scope == "x8")) {
        std::string key = weapon_name + "_" + scope;
        if (saved_zoom_values.count(key)) {
            return saved_zoom_values[key];
        }
    }
    // Use default zoom values from config.json
    if (scope_zoom_configs.count(scope)) {
        return scope_zoom_configs[scope].max;  // Use max as default
    }
    // Fallback default values
    if (scope == "x6") return 1.6;
    if (scope == "x8") return 2.0;
    return 1.0;
}

// Pose recognition function (only for right-click aiming)
void recognizePoseAndCar() {
    if (!regions.count("poses") || !regions.count("car")) {
        if (g_debug_mode) {
            std::cout << "[DEBUG] Missing regions - poses: " << regions.count("poses") << ", car: " << regions.count("car") << std::endl;
        }
        return;
    }

    // Store previous states to detect changes
    std::string prev_pose = current_pose;
    std::string prev_car = current_car_status;

    // Capture pose region
    cv::Mat pose_image = captureRegion(regions["poses"]);
    if (!pose_image.empty()) {
        std::string pose_result = recognizeRegion(pose_image, "poses", g_debug_mode);
        if (g_debug_mode) {
            std::cout << "[POSE DEBUG] Raw result: '" << pose_result << "'" << std::endl;
        }
        if (!pose_result.empty()) {
            current_pose = pose_result;
            if (g_debug_mode) {
                std::cout << "[POSE DEBUG] Updated current_pose to: '" << current_pose << "'" << std::endl;
            }
        } else {
            if (g_debug_mode) {
                std::cout << "[POSE DEBUG] No pose detected, keeping: '" << current_pose << "'" << std::endl;
            }
        }
    } else {
        if (g_debug_mode) {
            std::cout << "[POSE DEBUG] Failed to capture pose region" << std::endl;
        }
    }

    // Capture car region
    cv::Mat car_image = captureRegion(regions["car"]);
    if (!car_image.empty()) {
        std::string car_result = recognizeRegion(car_image, "car", g_debug_mode);
        if (g_debug_mode) {
            std::cout << "[CAR DEBUG] Raw result: '" << car_result << "'" << std::endl;
        }
        current_car_status = car_result.empty() ? "none" : car_result;
        if (g_debug_mode) {
            std::cout << "[CAR DEBUG] Updated current_car_status to: '" << current_car_status << "'" << std::endl;
        }
    } else {
        if (g_debug_mode) {
            std::cout << "[CAR DEBUG] Failed to capture car region" << std::endl;
        }
    }

    // Show current status (debug mode only)
    if (g_debug_mode) {
        std::cout << "[STATUS] Current: Pose='" << current_pose << "', Car='" << current_car_status << "'" << std::endl;
    }

    // Only log changes, don't write immediately (will be written by timer)
    if (current_pose != prev_pose || current_car_status != prev_car) {
        std::cout << "[CHANGE DETECTED] Pose: '" << prev_pose << "' -> '" << current_pose << "'" << std::endl;
        std::cout << "[CHANGE DETECTED] Car: '" << prev_car << "' -> '" << current_car_status << "'" << std::endl;
        if (g_debug_mode) {
            std::cout << "[INFO] Changes will be written by 500ms timer" << std::endl;
        }
    }
}

// Continuous pose recognition thread (300ms interval)
void poseRecognitionLoop() {
    while (pose_recognition_active && is_running) {
        recognizePoseAndCar();
        std::this_thread::sleep_for(std::chrono::milliseconds(300));
    }
}

// Start pose recognition
void startPoseRecognition() {
    if (!pose_recognition_active) {
        pose_recognition_active = true;
        pose_recognition_thread = std::thread(poseRecognitionLoop);
        std::cout << "Started pose recognition (300ms interval)" << std::endl;
    }
}

// Stop pose recognition
void stopPoseRecognition() {
    if (pose_recognition_active) {
        pose_recognition_active = false;
        if (pose_recognition_thread.joinable()) {
            pose_recognition_thread.detach();
        }
        std::cout << "Stopped pose recognition" << std::endl;
    }
}

// Reset pose and car status
void resetPoseStatus() {
    current_pose = "none";
    current_car_status = "none";
    std::cout << "Reset pose and car status to 'none'" << std::endl;
}

// Timed weapon config writing loop (500ms interval)
void configWriteLoop() {
    while (config_write_active && is_running) {
        WeaponConfig& current_config = (current_weapon_slot == 1) ? rifle_config : sniper_config;
        if (!current_config.isEmpty()) {
            writeWeaponConfig(current_config);
            std::cout << "[TIMER WRITE] Config written with pose='" << current_pose << "', car='" << current_car_status << "'" << std::endl;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
}

// Start timed config writing
void startConfigWriting() {
    if (!config_write_active) {
        config_write_active = true;
        config_write_thread = std::thread(configWriteLoop);
        std::cout << "Started timed config writing (500ms interval)" << std::endl;
    }
}

// Stop timed config writing
void stopConfigWriting() {
    if (config_write_active) {
        config_write_active = false;
        if (config_write_thread.joinable()) {
            config_write_thread.detach();
        }
        std::cout << "Stopped timed config writing" << std::endl;
    }
}

// Trigger recognition with bag detection and enhanced features
void triggerRecognition() {
    std::cout << "Tab key pressed - waiting for bag interface..." << std::endl;

    // Feature 3: Skip recognition if already done after tab (second tab closes bag)
    if (tab_recognition_done) {
        std::cout << "Recognition already done, skipping (second tab closes bag)" << std::endl;
        tab_recognition_done = false;  // Reset for next cycle
        return;
    }

    // Wait 100ms for bag interface to open
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // Check if bag interface is open
    if (!isBagInterfaceOpen()) {
        std::cout << "❌ Bag interface not detected - recognition cancelled" << std::endl;
        std::cout << "Please make sure you're in the inventory/bag interface" << std::endl;
        return;
    }

    std::cout << "✅ Bag interface detected - starting weapon recognition..." << std::endl;

    // Reset pose and car status when entering bag interface
    resetPoseStatus();

    // Store previous weapon names for zoom preservation
    std::string prev_rifle_name = rifle_config.weapon_name;
    std::string prev_sniper_name = sniper_config.weapon_name;

    // Enable debug mode for detailed analysis
    bool debug_mode = false; // Set to true for debugging

    // Sequential recognition with debug (not parallel for clearer debug output)
    if (debug_mode) {
        std::cout << "\n=== RIFLE RECOGNITION DEBUG ===" << std::endl;
        rifle_config = recognizeWeaponConfig(true);

        std::cout << "\n=== SNIPER RECOGNITION DEBUG ===" << std::endl;
        sniper_config = recognizeWeaponConfig(false);
    } else {
        // Parallel recognition of rifle and sniper
        auto rifle_future = std::async(std::launch::async, []() {
            return recognizeWeaponConfig(true);
        });

        auto sniper_future = std::async(std::launch::async, []() {
            return recognizeWeaponConfig(false);
        });

        rifle_config = rifle_future.get();
        sniper_config = sniper_future.get();
    }

    // Feature 2: Restore saved zoom values if weapon name hasn't changed
    if (!rifle_config.weapon_name.empty() && rifle_config.weapon_name == prev_rifle_name) {
        rifle_config.scope_zoom = getSavedZoomValue(rifle_config.weapon_name, rifle_config.scopes);
    }
    if (!sniper_config.weapon_name.empty() && sniper_config.weapon_name == prev_sniper_name) {
        sniper_config.scope_zoom = getSavedZoomValue(sniper_config.weapon_name, sniper_config.scopes);
    }

    // Feature 1: Auto output current weapon config after recognition
    WeaponConfig& current_config = (current_weapon_slot == 1) ? rifle_config : sniper_config;
    std::string weapon_type = (current_weapon_slot == 1) ? "Rifle" : "Sniper";

    if (!current_config.weapon_name.empty()) {
        writeWeaponConfig(current_config);
        std::cout << "✅ Auto-output " << weapon_type << " config to C:\\Temp\\weapon.lua" << std::endl;
        std::cout << "   📋 Weapon: " << current_config.weapon_name << std::endl;
        std::cout << "   🔫 Muzzle: " << current_config.muzzles << " | Grip: " << current_config.grips << std::endl;
        std::cout << "   🎯 Scope: " << current_config.scopes << " (Zoom: " << std::fixed << std::setprecision(2) << current_config.scope_zoom << ")" << std::endl;
        std::cout << "   🔧 Stock: " << current_config.stocks << " | Pose: " << current_pose << " | Car: " << current_car_status << std::endl;
    } else {
        std::cout << "⚠️  Current " << weapon_type << " weapon not detected - no config written" << std::endl;
    }

    // Mark that recognition was done after tab
    tab_recognition_done = true;

    // Output recognition results
    std::cout << "Rifle configuration recognition completed" << std::endl;
    std::cout << "   Weapon: " << rifle_config.weapon_name << std::endl;
    std::cout << "   Attachments: Muzzle=" << rifle_config.muzzles << ", Grip=" << rifle_config.grips << ", Scope=" << rifle_config.scopes << std::endl;
    std::cout << "   Stock=" << rifle_config.stocks << ", Pose=" << rifle_config.poses << ", Zoom=" << rifle_config.scope_zoom << std::endl;
    std::cout << "   Vehicle=" << rifle_config.car << std::endl;

    std::cout << "Sniper configuration recognition completed" << std::endl;
    std::cout << "   Weapon: " << sniper_config.weapon_name << std::endl;
    std::cout << "   Attachments: Muzzle=" << sniper_config.muzzles << ", Grip=" << sniper_config.grips << ", Scope=" << sniper_config.scopes << std::endl;
    std::cout << "   Stock=" << sniper_config.stocks << ", Pose=" << sniper_config.poses << ", Zoom=" << sniper_config.scope_zoom << std::endl;
    std::cout << "   Vehicle=" << sniper_config.car << std::endl;
}

// Switch weapon
void switchWeapon(int slot) {
    current_weapon_slot = slot;
    WeaponConfig& config = (slot == 1) ? rifle_config : sniper_config;
    std::string weapon_name = (slot == 1) ? "Rifle" : "Sniper";

    std::cout << "Switching to " << weapon_name << " (slot " << slot << ")" << std::endl;

    if (!config.isEmpty()) {
        writeWeaponConfig(config);
        std::cout << "✅ Output " << weapon_name << " config to C:\\Temp\\weapon.lua" << std::endl;
        std::cout << "   📋 Weapon: " << config.weapon_name << std::endl;
        std::cout << "   🔫 Muzzle: " << config.muzzles << " | Grip: " << config.grips << std::endl;
        std::cout << "   🎯 Scope: " << config.scopes << " (Zoom: " << std::fixed << std::setprecision(2) << config.scope_zoom << ")" << std::endl;
        std::cout << "   🔧 Stock: " << config.stocks << " | Pose: " << current_pose << " | Car: " << current_car_status << std::endl;
    } else {
        std::cout << "⚠️  Warning: " << weapon_name << " config is empty, please press ` key to recognize first" << std::endl;
    }
}

// Quick recognize current weapon
void quickRecognizeCurrentWeapon() {
    bool is_rifle = (current_weapon_slot == 1);
    std::string weapon_name = is_rifle ? "Rifle" : "Sniper";

    std::cout << "Quick recognizing current " << weapon_name << "..." << std::endl;

    WeaponConfig config = recognizeWeaponConfig(is_rifle);

    if (is_rifle) {
        rifle_config = config;
    } else {
        sniper_config = config;
    }

    if (!config.isEmpty()) {
        writeWeaponConfig(config);
        std::cout << "Quick recognition completed: " << weapon_name << " -> " << config.weapon_name << std::endl;
        std::cout << "Output to C:\\Temp\\weapon.lua" << std::endl;
    } else {
        std::cout << "Quick recognition failed for " << weapon_name << std::endl;
    }
}

// Handle scope zoom adjustment
void adjustScopeZoom(int delta) {
    WeaponConfig& current_config = (current_weapon_slot == 1) ? rifle_config : sniper_config;

    if (current_config.scopes == "x6" || current_config.scopes == "x8") {
        // Get zoom configuration from config.json
        ScopeZoomConfig zoom_config;
        if (scope_zoom_configs.count(current_config.scopes)) {
            zoom_config = scope_zoom_configs[current_config.scopes];
        } else {
            // Fallback defaults
            zoom_config = (current_config.scopes == "x6") ?
                         ScopeZoomConfig{1.0, 1.6, 0.1} :
                         ScopeZoomConfig{1.0, 2.0, 0.1};
        }

        double zoom_step = zoom_config.step;
        double min_zoom = zoom_config.min;
        double max_zoom = zoom_config.max;

        if (delta > 0) {  // Scroll up, zoom in
            scope_zoom = std::min(scope_zoom + zoom_step, max_zoom);
        } else {  // Scroll down, zoom out
            scope_zoom = std::max(scope_zoom - zoom_step, min_zoom);
        }

        current_config.scope_zoom = scope_zoom;

        std::cout << current_config.scopes << " scope zoom adjusted to: " << std::fixed << std::setprecision(2) << scope_zoom << std::endl;

        // Feature 2: Save zoom value when adjusted
        saveZoomValue(current_config.weapon_name, current_config.scopes, scope_zoom);

        // Note: Config will be written by the timed writing loop (500ms interval)
        // This prevents file conflicts with Logitech script reading
    } else {
        std::cout << "Zoom adjustment only available for x6/x8 scopes" << std::endl;
    }
}

// Mouse hook handler
LRESULT CALLBACK mouseProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0) {
        MSLLHOOKSTRUCT* mouse_data = (MSLLHOOKSTRUCT*)lParam;

        switch (wParam) {
        case WM_RBUTTONDOWN:
            if (in_bag_interface) {
                // In bag interface, trigger re-recognition
                std::cout << "Right mouse click detected in bag interface - triggering re-recognition..." << std::endl;

                // Small delay to let the click action complete
                std::thread([]{
                    std::this_thread::sleep_for(std::chrono::milliseconds(200));

                    // Check if still in bag interface
                    if (isBagInterfaceOpen()) {
                        std::cout << "Re-recognizing weapons after bag interaction..." << std::endl;

                        // Store previous weapon names for zoom preservation
                        std::string prev_rifle_name = rifle_config.weapon_name;
                        std::string prev_sniper_name = sniper_config.weapon_name;

                        // Re-recognize weapons
                        auto rifle_future = std::async(std::launch::async, []() {
                            return recognizeWeaponConfig(true);
                        });

                        auto sniper_future = std::async(std::launch::async, []() {
                            return recognizeWeaponConfig(false);
                        });

                        rifle_config = rifle_future.get();
                        sniper_config = sniper_future.get();

                        // Restore saved zoom values if weapon name hasn't changed
                        if (!rifle_config.weapon_name.empty() && rifle_config.weapon_name == prev_rifle_name) {
                            rifle_config.scope_zoom = getSavedZoomValue(rifle_config.weapon_name, rifle_config.scopes);
                        }
                        if (!sniper_config.weapon_name.empty() && sniper_config.weapon_name == prev_sniper_name) {
                            sniper_config.scope_zoom = getSavedZoomValue(sniper_config.weapon_name, sniper_config.scopes);
                        }

                        // Auto output current weapon config
                        WeaponConfig& current_config = (current_weapon_slot == 1) ? rifle_config : sniper_config;
                        std::string weapon_type = (current_weapon_slot == 1) ? "Rifle" : "Sniper";

                        if (!current_config.weapon_name.empty()) {
                            writeWeaponConfig(current_config);
                            std::cout << "✅ Auto-output " << weapon_type << " config after bag interaction" << std::endl;
                            std::cout << "   📋 Weapon: " << current_config.weapon_name << std::endl;
                            std::cout << "   🔫 Muzzle: " << current_config.muzzles << " | Grip: " << current_config.grips << std::endl;
                            std::cout << "   🎯 Scope: " << current_config.scopes << " (Zoom: " << std::fixed << std::setprecision(2) << current_config.scope_zoom << ")" << std::endl;
                            std::cout << "   🔧 Stock: " << current_config.stocks << " | Pose: " << current_pose << " | Car: " << current_car_status << std::endl;
                        } else {
                            std::cout << "⚠️  Current " << weapon_type << " weapon not detected after bag interaction" << std::endl;
                        }
                    }
                }).detach();
            } else {
                // Normal right-click behavior (aiming)
                right_button_pressed = true;
                std::cout << "Right mouse button pressed - scope zoom mode enabled" << std::endl;
                // Start pose recognition when aiming (right-click)
                startPoseRecognition();
                // Start timed config writing when aiming
                startConfigWriting();
            }
            break;

        case WM_RBUTTONUP:
            right_button_pressed = false;
            std::cout << "Right mouse button released" << std::endl;
            // Stop pose recognition when not aiming
            stopPoseRecognition();
            // Stop timed config writing when not aiming
            stopConfigWriting();
            break;

        case WM_MOUSEWHEEL:
            if (right_button_pressed) {
                short delta = GET_WHEEL_DELTA_WPARAM(mouse_data->mouseData);
                int scroll_direction = (delta > 0) ? 1 : -1;
                std::thread([scroll_direction]() { adjustScopeZoom(scroll_direction); }).detach();
            }
            break;

        // Feature 4: Re-recognize when clicking in bag interface
        case WM_LBUTTONDOWN:
        case WM_MBUTTONDOWN:
            if (in_bag_interface) {
                std::cout << "Mouse click detected in bag interface - triggering re-recognition..." << std::endl;

                // Small delay to let the click action complete
                std::thread([]{
                    std::this_thread::sleep_for(std::chrono::milliseconds(200));

                    // Check if still in bag interface
                    if (isBagInterfaceOpen()) {
                        std::cout << "Re-recognizing weapons after bag interaction..." << std::endl;

                        // Store previous weapon names for zoom preservation
                        std::string prev_rifle_name = rifle_config.weapon_name;
                        std::string prev_sniper_name = sniper_config.weapon_name;

                        // Re-recognize weapons
                        auto rifle_future = std::async(std::launch::async, []() {
                            return recognizeWeaponConfig(true);
                        });

                        auto sniper_future = std::async(std::launch::async, []() {
                            return recognizeWeaponConfig(false);
                        });

                        rifle_config = rifle_future.get();
                        sniper_config = sniper_future.get();

                        // Restore saved zoom values if weapon name hasn't changed
                        if (!rifle_config.weapon_name.empty() && rifle_config.weapon_name == prev_rifle_name) {
                            rifle_config.scope_zoom = getSavedZoomValue(rifle_config.weapon_name, rifle_config.scopes);
                        }
                        if (!sniper_config.weapon_name.empty() && sniper_config.weapon_name == prev_sniper_name) {
                            sniper_config.scope_zoom = getSavedZoomValue(sniper_config.weapon_name, sniper_config.scopes);
                        }

                        // Auto output current weapon config
                        WeaponConfig& current_config = (current_weapon_slot == 1) ? rifle_config : sniper_config;
                        std::string weapon_type = (current_weapon_slot == 1) ? "Rifle" : "Sniper";

                        if (!current_config.weapon_name.empty()) {
                            writeWeaponConfig(current_config);
                            std::cout << "✅ Auto-output " << weapon_type << " config after bag interaction" << std::endl;
                            std::cout << "   📋 Weapon: " << current_config.weapon_name << std::endl;
                            std::cout << "   🔫 Muzzle: " << current_config.muzzles << " | Grip: " << current_config.grips << std::endl;
                            std::cout << "   🎯 Scope: " << current_config.scopes << " (Zoom: " << std::fixed << std::setprecision(2) << current_config.scope_zoom << ")" << std::endl;
                            std::cout << "   🔧 Stock: " << current_config.stocks << " | Pose: " << current_pose << " | Car: " << current_car_status << std::endl;
                        } else {
                            std::cout << "⚠️  Current " << weapon_type << " weapon not detected after bag interaction" << std::endl;
                        }
                    }
                }).detach();
            }
            break;
        }
    }

    return CallNextHookEx(nullptr, nCode, wParam, lParam);
}

// Keyboard hook handler
LRESULT CALLBACK keyboardProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0 && wParam == WM_KEYDOWN) {
        KBDLLHOOKSTRUCT* kbd = (KBDLLHOOKSTRUCT*)lParam;

        switch (kbd->vkCode) {
        case VK_OEM_3: // ` key
            std::thread(triggerRecognition).detach();
            break;
        case '1':
            std::thread([]() { switchWeapon(1); }).detach();
            break;
        case '2':
            std::thread([]() { switchWeapon(2); }).detach();
            break;
        case VK_TAB: // Tab key for full recognition (same as ` key)
            std::thread(triggerRecognition).detach();
            break;
        case VK_ESCAPE:
            is_running = false;
            break;
        }
    }

    return CallNextHookEx(nullptr, nCode, wParam, lParam);
}

int main(int argc, char* argv[]) {
    // Check for debug flag
    for (int i = 1; i < argc; i++) {
        if (std::string(argv[i]) == "--debug") {
            g_debug_mode = true;
            break;
        }
    }
    std::cout << "PUBG武器自动识别系统 C++版本" << std::endl;
    std::cout << "==============================" << std::endl;

    // System diagnostics
    std::cout << "\n[系统信息] 系统诊断:" << std::endl;

    // Windows version
    OSVERSIONINFO osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFO));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
    if (GetVersionEx(&osvi)) {
        std::cout << "[系统信息] Windows版本: " << osvi.dwMajorVersion << "." << osvi.dwMinorVersion << std::endl;
    }

    // Check if running as administrator
    BOOL is_admin = FALSE;
    PSID admin_group = NULL;
    SID_IDENTIFIER_AUTHORITY nt_authority = SECURITY_NT_AUTHORITY;
    if (AllocateAndInitializeSid(&nt_authority, 2, SECURITY_BUILTIN_DOMAIN_RID, DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &admin_group)) {
        CheckTokenMembership(NULL, admin_group, &is_admin);
        FreeSid(admin_group);
    }
    std::cout << "[系统信息] 管理员权限运行: " << (is_admin ? "是" : "否") << std::endl;

    // Detect screen resolution
    int screen_width = GetSystemMetrics(SM_CXSCREEN);
    int screen_height = GetSystemMetrics(SM_CYSCREEN);
    std::cout << "[系统信息] 屏幕分辨率: " << screen_width << "x" << screen_height << std::endl;

    // DPI information
    HDC hdc = GetDC(NULL);
    int dpi_x = GetDeviceCaps(hdc, LOGPIXELSX);
    int dpi_y = GetDeviceCaps(hdc, LOGPIXELSY);
    ReleaseDC(NULL, hdc);
    std::cout << "[系统信息] DPI: " << dpi_x << "x" << dpi_y << " (" << (dpi_x * 100 / 96) << "%)" << std::endl;

    std::cout << std::endl;

    // Load configuration for current resolution
    std::cout << "正在加载配置..." << std::endl;
    loadConfig(screen_width, screen_height);

    // Initialize DXGI screen capture
    std::cout << "正在初始化DXGI屏幕捕获..." << std::endl;
    if (!g_dxgi_capture.Initialize()) {
        std::cout << "⚠️  DXGI初始化失败，将使用GDI备用方案" << std::endl;
    } else {
        std::cout << "✅ DXGI屏幕捕获初始化成功" << std::endl;
    }

    // Load templates for current resolution
    std::cout << "正在加载模板..." << std::endl;
    if (!loadTemplates(screen_width, screen_height)) {
        std::cerr << "模板加载失败" << std::endl;
        return -1;
    }

    // Check if we're using native resolution or fallback
    std::string current_resolution = std::to_string(screen_width) + "x" + std::to_string(screen_height);
    std::string config_check_path = "nc/" + current_resolution + "/config.json";
    if (std::filesystem::exists(config_check_path)) {
        std::cout << "✅ 使用原生 " << current_resolution << " 配置" << std::endl;
    } else {
        std::cout << "⚠️  使用备用配置（可能影响准确性）" << std::endl;
        std::cout << "   建议添加 " << current_resolution << " 模板以提高准确性" << std::endl;
    }

    // Display detailed configuration information (debug mode only)
    if (g_debug_mode) {
        std::cout << "\n========================================" << std::endl;
        std::cout << "=== CONFIGURATION DETAILS ===" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "Config file path: " << config_check_path << std::endl;
        std::cout << "Screen Resolution: " << screen_width << "x" << screen_height << std::endl;

        // Display key regions for verification
        std::cout << "\n=== KEY REGIONS ===" << std::endl;
        if (regions.count("bag")) {
            auto& bag = regions["bag"];
            std::cout << "Bag region: x=" << bag.x << ", y=" << bag.y << ", w=" << bag.width << ", h=" << bag.height << std::endl;
        }
        if (regions.count("weapon_name_rifle")) {
            auto& weapon = regions["weapon_name_rifle"];
            std::cout << "Rifle weapon region: x=" << weapon.x << ", y=" << weapon.y << ", w=" << weapon.width << ", h=" << weapon.height << std::endl;
        }
        if (regions.count("weapon_name_sniper")) {
            auto& weapon = regions["weapon_name_sniper"];
            std::cout << "Sniper weapon region: x=" << weapon.x << ", y=" << weapon.y << ", w=" << weapon.width << ", h=" << weapon.height << std::endl;
        }
        if (regions.count("muzzles_rifle")) {
            auto& muzzle = regions["muzzles_rifle"];
            std::cout << "Rifle muzzle region: x=" << muzzle.x << ", y=" << muzzle.y << ", w=" << muzzle.width << ", h=" << muzzle.height << std::endl;
        }
        if (regions.count("muzzles_sniper")) {
            auto& muzzle = regions["muzzles_sniper"];
            std::cout << "Sniper muzzle region: x=" << muzzle.x << ", y=" << muzzle.y << ", w=" << muzzle.width << ", h=" << muzzle.height << std::endl;
        }
        if (regions.count("grips_rifle")) {
            auto& grip = regions["grips_rifle"];
            std::cout << "Rifle grip region: x=" << grip.x << ", y=" << grip.y << ", w=" << grip.width << ", h=" << grip.height << std::endl;
        }
        if (regions.count("grips_sniper")) {
            auto& grip = regions["grips_sniper"];
            std::cout << "Sniper grip region: x=" << grip.x << ", y=" << grip.y << ", w=" << grip.width << ", h=" << grip.height << std::endl;
        }
        if (regions.count("scopes_rifle")) {
            auto& scope = regions["scopes_rifle"];
            std::cout << "Rifle scope region: x=" << scope.x << ", y=" << scope.y << ", w=" << scope.width << ", h=" << scope.height << std::endl;
        }
        if (regions.count("scopes_sniper")) {
            auto& scope = regions["scopes_sniper"];
            std::cout << "Sniper scope region: x=" << scope.x << ", y=" << scope.y << ", w=" << scope.width << ", h=" << scope.height << std::endl;
        }
        if (regions.count("stocks_rifle")) {
            auto& stock = regions["stocks_rifle"];
            std::cout << "Rifle stock region: x=" << stock.x << ", y=" << stock.y << ", w=" << stock.width << ", h=" << stock.height << std::endl;
        }
        if (regions.count("stocks_sniper")) {
            auto& stock = regions["stocks_sniper"];
            std::cout << "Sniper stock region: x=" << stock.x << ", y=" << stock.y << ", w=" << stock.width << ", h=" << stock.height << std::endl;
        }
        std::cout << "========================================\n" << std::endl;
    }



    // Install keyboard hook
    keyboard_hook = SetWindowsHookEx(WH_KEYBOARD_LL, keyboardProc, GetModuleHandle(nullptr), 0);
    if (!keyboard_hook) {
        std::cerr << "Keyboard hook installation failed" << std::endl;
        return -1;
    }

    // Install mouse hook
    mouse_hook = SetWindowsHookEx(WH_MOUSE_LL, mouseProc, GetModuleHandle(nullptr), 0);
    if (!mouse_hook) {
        std::cerr << "Mouse hook installation failed" << std::endl;
        UnhookWindowsHookEx(keyboard_hook);
        return -1;
    }

    std::cout << "系统初始化完成！" << std::endl;
    std::cout << "\n增强功能:" << std::endl;
    std::cout << "✅ Tab识别后自动输出当前武器配置" << std::endl;
    std::cout << "✅ 按武器保存缩放值（武器不变时保持缩放）" << std::endl;
    std::cout << "✅ 智能Tab处理（第二次Tab关闭背包，不重复识别）" << std::endl;
    std::cout << "✅ 背包点击自动重识别（装备变化时）" << std::endl;
    std::cout << "✅ 智能姿势识别（仅在右键瞄准时）" << std::endl;
    std::cout << "✅ 动态分辨率支持（自动检测屏幕分辨率）" << std::endl;
    std::cout << "✅ 可配置瞄镜缩放范围（从config.json读取最小/最大/步长）" << std::endl;
    std::cout << "✅ DXGI硬件加速屏幕捕获（带GDI备用方案）" << std::endl;
    std::cout << "\n姿势状态: none/stand/down/crawl | 载具状态: none/car" << std::endl;
    std::cout << "\n使用方法:" << std::endl;
    std::cout << "- 按 ` 或 TAB 键识别武器（重置姿势为'none'）" << std::endl;
    std::cout << "- 按 1 键手动输出步枪配置" << std::endl;
    std::cout << "- 按 2 键手动输出狙击枪配置" << std::endl;
    std::cout << "- 右键+滚轮调整瞄镜缩放（仅x6/x8）" << std::endl;
    std::cout << "- 按住右键开始姿势识别+500ms配置写入" << std::endl;
    std::cout << "- 在背包界面点击自动重识别装备变化" << std::endl;
    std::cout << "- 按 ESC 键退出程序" << std::endl;
    std::cout << "\n系统运行中，等待按键操作..." << std::endl;
    std::cout << "C++版本性能: CPU < 5%, 内存 < 30MB" << std::endl;

    // Register exit cleanup function
    std::atexit(cleanupOnExit);

    // Main loop
    MSG msg;
    while (is_running && GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // Manual cleanup (in case atexit is not called)
    cleanupOnExit();

    // Additional cleanup
    stopPoseRecognition();  // Stop pose recognition thread
    stopConfigWriting();    // Stop config writing thread
    g_dxgi_capture.Cleanup();  // Cleanup DXGI resources
    if (keyboard_hook) {
        UnhookWindowsHookEx(keyboard_hook);
    }
    if (mouse_hook) {
        UnhookWindowsHookEx(mouse_hook);
    }
    std::cout << "\n程序退出" << std::endl;

    return 0;
}
