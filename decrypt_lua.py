#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lua代码解密工具
用于解密混淆的Lua代码，将ASCII转义序列转换为可读字符串
"""

import re

def decode_ascii_sequences(text):
    """将ASCII转义序列转换为可读字符串"""
    def replace_ascii(match):
        ascii_sequence = match.group(1)
        # 提取所有的数字序列
        numbers = re.findall(r'\\(\d+)', ascii_sequence)
        result = ""
        for num in numbers:
            try:
                result += chr(int(num))
            except ValueError:
                continue
        return f'"{result}"'

    # 匹配形如 "\116\111\110\117\109\98\101\114" 的模式
    pattern = r'"((?:\\\\d+)+)"'
    return re.sub(pattern, replace_ascii, text)

def format_lua_code(code):
    """基本格式化Lua代码"""
    # 替换分号为换行
    code = code.replace(';', ';\n')

    # 在关键字前添加换行
    keywords = ['local ', 'function', 'if ', 'while ', 'for ', 'end', 'then', 'else']
    for keyword in keywords:
        code = code.replace(keyword, '\n' + keyword)

    # 处理大括号
    code = code.replace('{', '{\n')
    code = code.replace('}', '\n}')

    # 清理多余的空行和空格
    lines = []
    for line in code.split('\n'):
        line = line.strip()
        if line:
            lines.append(line)

    return '\n'.join(lines)

def main():
    # 从文件读取最后一行的加密代码
    try:
        with open('开镜50垂直1（垂直可随意修改）.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            encrypted_line = lines[-1].strip()  # 获取最后一行
    except FileNotFoundError:
        print("文件未找到，使用示例代码进行演示")
        # 这里是文件最后一行的加密代码（截取部分）
        encrypted_line = 'local a=95;local b=73;local c=79;local d=0==1;local e=not d;local f=nil;local g=""local h=_G;local i=_ENV;local j=h["\\116\\111\\110\\117\\109\\98\\101\\114"]'

    print("开始解密Lua代码...")
    print(f"原始代码长度: {len(encrypted_line)} 字符")

    # 第一步：解码ASCII转义序列
    print("\n步骤1: 解码ASCII转义序列...")
    decoded = decode_ascii_sequences(encrypted_line)

    # 第二步：格式化代码
    print("步骤2: 格式化代码...")
    formatted = format_lua_code(decoded)

    print("\n" + "="*60)
    print("解密后的Lua代码:")
    print("="*60)
    print(formatted)

    # 保存解密后的代码
    with open('decrypted_code.lua', 'w', encoding='utf-8') as f:
        f.write(formatted)

    print(f"\n解密后的代码已保存到 'decrypted_code.lua' 文件中")

    # 分析代码功能
    print("\n" + "="*60)
    print("代码功能分析:")
    print("="*60)

    if "tonumber" in formatted:
        print("✓ 包含数字转换函数")
    if "GetRunningTime" in formatted:
        print("✓ 包含时间获取函数 - 可能用于计时")
    if "MoveMouse" in formatted:
        print("✓ 包含鼠标移动函数 - 用于控制鼠标")
    if "IsMouseButtonPressed" in formatted:
        print("✓ 包含鼠标按键检测 - 用于检测鼠标状态")
    if "apply_recoil" in formatted:
        print("✓ 包含后坐力应用函数 - 这是游戏辅助功能")

    print("\n总结: 这段代码是游戏鼠标宏的核心逻辑，用于自动控制鼠标移动以补偿武器后坐力")

if __name__ == "__main__":
    main()
