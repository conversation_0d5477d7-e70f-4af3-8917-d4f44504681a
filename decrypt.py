import re

def decrypt_octal_string(encrypted_string):
    """解密八进制字符串"""
    # 查找所有八进制转义序列
    octal_pattern = r'\\(\d{3})'
    
    def replace_octal(match):
        octal_code = match.group(1)
        try:
            # 将八进制转换为字符
            char_code = int(octal_code, 8)
            return chr(char_code)
        except:
            return match.group(0)
    
    # 替换所有八进制转义序列
    decrypted = re.sub(octal_pattern, replace_octal, encrypted_string)
    return decrypted

def decrypt_file():
    """解密文件最后一行"""
    try:
        with open('开镜50垂直1（垂直可随意修改）.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            print("文件为空")
            return
        
        # 获取最后一行
        last_line = lines[-1].strip()
        print("原始加密内容:")
        print(last_line[:200] + "..." if len(last_line) > 200 else last_line)
        print("\n" + "="*50 + "\n")
        
        # 解密内容
        decrypted = decrypt_octal_string(last_line)
        
        print("解密后的内容:")
        print(decrypted)
        
        # 保存解密结果
        with open('decrypted_result.txt', 'w', encoding='utf-8') as f:
            f.write(decrypted)
        
        print(f"\n解密结果已保存到 decrypted_result.txt")
        
    except Exception as e:
        print(f"解密过程中出现错误: {e}")

if __name__ == "__main__":
    decrypt_file() 