local a=95;local b=73;local c=79;local d=0==1;local e=not d;local f=nil;local g=""local h=_G;local i=_ENV;local j=h["tonumber"]local k=function(...)h["ceil_and_cache"]=function(l)local m=h["math"]["floor"](l)h["decimal_cache"]=h["decimal_cache"]+l-m;if h["decimal_cache"]>=j("1")then m=m+j("1")h["decimal_cache"]=h["decimal_cache"]-j("1")end;return m end;local n=d;local o=d;local p={}local q=j("0")h["Sleep2"]=function(r)local s=h["GetRunningTime"]()while h["GetRunningTime"]()-s<=r do end end;local t=j("0")h["apply_recoil"]=function(u,v,w,x,y,z,A,B)if not h["is_authorized"]()then return end;local C={"MINI","SKS","MK12","SLR","QBU"}local D=h["IsKeyLockOn"]("capslock")local E="default"if D and h["table"]["contains"](C,u)then E="burst"end;local F=h["recoil_patterns"][u]and h["recoil_patterns"][u][E]or h["recoil_patterns"][u]local G=h["weapon_intervals"][u]if not F or not G then h["OutputLogMessage"]("æªæ¾å°æ­¦å¨çåæªåæ°: %s
",u)return end;local H=h["attachment_multipliers"][u]and h["attachment_multipliers"][u]["poses"]local I=j("1")local J=j("1")if H then I=H[z]or j("1")J=H[z]or j("1")if D then I=H["standBurst"]or I;J=H["downBurst"]or J end end;local K=j("0")local L=j("0")local M=j("0")local N=B;local O={"MK47","M16","None"}local P={"MINI","SKS","MK12","SLR","QBU"}local Q=j("1")if h["table"]["contains"](O,u)then while h["IsMouseButtonPressed"](j("1"))and(h["IsMouseButtonPressed"](j("3"))or h["IsMouseButtonPressed"](j("4")))do local R=h["GetRunningTime"]()K=h["math"]["ceil"]((R-q)/G)for S,T in h["ipairs"](F)do if T[j("1")]==K then z,M=h["read_poses_file"]()h["multiplier"]=h["calculate_recoil_multiplier"](u,v,w,x,y,z,A,h["car"],K)local U=h["ceil_and_cache"](T[j("2")]*h["multiplier"])local V=h["handle_x_movement"](M)if K==j("1")then for W=j("1"),Q do h["MoveMouseRelative"](V,h["ceil_and_cache"](U+h["first_shot_offset"])/Q)end;h["PressAndReleaseKey"]("F8")else h["MoveMouseRelative"](V,U)h["PressAndReleaseKey"]("F8")end;if o then h["MoveMouseRelative"](j("1"),j("0"))end;break end end;h["Sleep2"](j("1"))if not h["IsMouseButtonPressed"](j("1"))then h["OLD"]=j("0")break end end elseif D and h["table"]["contains"](P,u)then while h["IsMouseButtonPressed"](j("1"))and(h["IsMouseButtonPressed"](j("3"))or h["IsMouseButtonPressed"](j("4")))do local R=h["GetRunningTime"]()K=h["math"]["ceil"]((R-q)/G)for S,T in h["ipairs"](F)do if T[j("1")]==K then z,M=h["read_poses_file"]()h["multiplier"]=h["calculate_recoil_multiplier"](u,v,w,x,y,z,A,h["car"],K)local U=h["ceil_and_cache"](T[j("2")]*h["multiplier"])local V=h["handle_x_movement"](M)if K==j("1")then for W=j("1"),Q do h["MoveMouseRelative"](V,h["ceil_and_cache"](U+h["first_shot_offset"])/Q)end;h["PressAndReleaseKey"]("F8")else h["MoveMouseRelative"](V,U)h["PressAndReleaseKey"]("F8")end;if o then h["OLD"]=j("0")break end;if o then h["MoveMouseRelative"](j("3"),j("0"))end;break end end;h["Sleep2"](j("1"))if not h["IsMouseButtonPressed"](j("1"))then break end end else local X=j("0")while h["IsMouseButtonPressed"](j("1"))and(h["IsMouseButtonPressed"](j("3"))or h["IsMouseButtonPressed"](j("4")))do local R=h["GetRunningTime"]()K=h["math"]["ceil"]((R-q)/G)for S,T in h["ipairs"](F)do if T[j("1")]==K then z,M=h["read_poses_file"]()h["multiplier"]=h["calculate_recoil_multiplier"](u,v,w,x,y,z,A,h["car"],K)local U=h["ceil_and_cache"](T[j("2")]*h["multiplier"])local V=h["handle_x_movement"](M)if K==j("1")then for W=j("1"),Q do h["MoveMouseRelative"](V,h["ceil_and_cache"](U+h["first_shot_offset"])/Q)end else h["MoveMouseRelative"](V,U)end;if not h["IsMouseButtonPressed"](j("1"))then h["OLD"]=j("0")break end;if o then h["MoveMouseRelative"](j("1"),j("0"))end;break end end;h["Sleep2"](j("1"))if not h["IsMouseButtonPressed"](j("1"))then break end end end end;local Y=f;local Z=f;local _=f;local a0=f;local a1=f;local a2=f;local a3=f;local a4=f;local a5=f;local a6=f;local function a7()local u,v,w,x,y,z,A,a8,a9,B=h["read_weapon_from_file"]()if u then Y=u;Z=v;_=w;a0=x;a1=y;a2=z;a3=A;a4=a8;a5=a9;a6=B end end;local aa=j("1")local ab=h["GetRunningTime"]()h["read_weapon_from_file"]=function()if not h["is_authorized"]()then return f end;h["weapon_name"]=f;h["scopes"]=f;h["muzzles"]=f;h["stocks"]=f;h["poses"]=f;h["shoot"]=f;h["car"]=f;h["cursor_x"]=j("0")h["dofile"](h["addr"])if h["weapon_name"]then local ac=h["string"]["format"]("%s+%s+%s+%s+%s+%s+%s+%s+%s",h["weapon_name"],h["muzzles"],h["grips"],h["scopes"],h["stocks"],h["poses"],h["scope_zoom"],h["shoot"],h["car"])h["OutputLogMessage"]("%s
",ac)return h["weapon_name"],h["muzzles"],h["grips"],h["scopes"],h["stocks"],h["poses"],h["scope_zoom"],h["shoot"],h["car"],h["cursor_x"]else h["OutputLogMessage"]("æªæ¾å°æ­¦å¨ä¿¡æ¯, ä½¿ç¨ä¸ä¸æ¬¡çæ­¦å¨ä¿¡æ¯
")return Y,Z,_,a0,a1,a2,a3,a4,a5,a6 end end;h["read_poses_file"]=function()if not h["is_authorized"]()then return f end;h["poses"]=f;h["cursor_x"]=j("0")h["dofile"](h["addr"])if h["weapon_name"]then a2=h["poses"]a6=h["cursor_x"]return h["poses"],h["cursor_x"]else return a2,a6 end end;h["OnEvent"]=function(ad,ae)if ad=="PROFILE_ACTIVATED"then h["EnablePrimaryMouseButtonEvents"](e)a7()elseif ad=="PROFILE_DEACTIVATED"then h["EnablePrimaryMouseButtonEvents"](d)elseif ad=="MOUSE_BUTTON_PRESSED"then local af=h["GetRunningTime"]()if af-ab>=aa then a7()ab=af end;if ae==j("1")then q=h["GetRunningTime"]()h["PressKey"]("F8")if Y then h["apply_recoil"](Y,Z,_,a0,a1,a2,a3,a5,a6)end elseif ae==j("2")then if Y then local ac=h["string"]["format"]("%s+%s+%s+%s+%s+%s+%s+%s",Y,Z,_,a0,a1,a2,a3,a5)h["OutputLogMessage"]("%s
",ac)end elseif ae==j("5")and h["IsModifierPressed"]("lctrl")then if Y then p[Y]=not p[Y]h["OutputLogMessage"]("weapon %s tututu %s
",Y,p[Y]and"on"or"close")end elseif ae==j("8")and h["IsModifierPressed"]("lctrl")then o=not o;h["OutputLogMessage"]("debug%s
",o and"on"or"close")elseif ae==j("9")then h["fastPickup"]()end elseif ad=="MOUSE_BUTTON_RELEASED"then if ae==j("1")then h["ReleaseKey"]("F8")h["MoveMouseRelative"](j("0"),j("0"))end end;if ad=="MOUSE_BUTTON_PRESSED"and ae==h["pick"]then h["autopick"]()end end;h["autopick"]=function()h["PressAndReleaseKey"]("tab")h["Sleep2"](j("50"))for ag=j("1"),j("5")do for ah=j("1"),j("5")do h["MoveMouseTo"](j("7800"),j("35000")-ah*j("5425"))h["PressMouseButton"](j("1"))h["MoveMouseTo"](j("32767")+ah*j("11"),j("12500")+ah*j("12"))h["ReleaseMouseButton"](j("1"))h["Sleep"](j("1"))end end;h["MoveMouseTo"](j("32767"),j("32767"))h["Sleep"](j("1"))h["PressAndReleaseKey"]("tab")end;local ai=j("0")local aj=j("0")h["pid_check"]=function(aj)if aj==j("0")then ai=j("0")return j("0")end;local ak=j("0")local al=j("0")ak=aj*j("1")al=ak*j("0.1")+ai;local am=ak+al;if al>j("2")then ai=j("2")elseif al<-j("2")then ai=-j("2")else ai=al end;return am end;h["calculate_recoil_multiplier"]=function(u,v,w,x,y,z,A,a9,K)local an=h["global_recoil_multiplier"]local ao=h["attachment_multipliers"][u]an=an*(h["base_coefficients"][u]or j("1"))if ao then local H=ao["poses"]local ap=z;local aq={"MINI","SKS","MK12","SLR","QBU"}if h["table"]["contains"](aq,u)and h["IsKeyLockOn"]("capslock")then ap=z=="stand"and"standBurst"or z=="down"and"downBurst"or z end;an=an*(H[ap]or j("1"))local function ar(as,at)local au=ao[as][at]if h["type"](au)=="table"and au["segments"]then for W,av in h["ipairs"](au["segments"])do if K<=av["count"]then an=an*av["multiplier"]return end end elseif h["type"](au)=="number"then an=an*au else an=an*(ao[as][at]or j("1"))end end;ar("muzzles",v)ar("grips",w)ar("scopes",x)ar("stocks",y)ar("car",a9)an=an*A end;an=an*(h["global_scope_multipliers"][x]or j("1"))local aw={"Berry","AUG","AKM","M416","ACE32","G36C","SCAR","QBZ","K2","M16","MK47","GROZA","FAMAS","PP19","TOM","UMP","UZI","VECTOR","MP5","P90","JS9","MP9","M249","MG3"}if h["IsModifierPressed"]("lshift")and h["table"]["contains"](aw,u)then an=an*h["global_breath_multiplier"]end;an=an*h["global_sensitivity_multiplier"]an=an*h["global_vertical_sensitivity_multiplier"]return an end;h["table"]["contains"]=function(ax,ay)for S,l in h["pairs"](ax)do if l==ay then return e end end;return d end;h["is_authorized"]=function()local az,aA=h["pcall"](h["dofile"],h["addr"])if not az then h["OutputLogMessage"]("Error loading weapon.lua: %s
",aA)return d end;return h["type"](h["muzzle"])=="string"and h["muzzle"]=="None"end;for u,aB in h["pairs"](h["recoil_patterns"])do for E,F in h["pairs"](aB)do for W,aC in h["ipairs"](F)do if W%j("2")==j("0")then F[W][j("2")]=(F[W][j("2")]+j("1"))/j("2")end end end end;h["G1_PRESSED"]=function()h["G1___"]=e;h["OnEvent"]("MOUSE_BUTTON_PRESSED",j("1"),"mouse")end;h["G1_RELEASED"]=function()h["G1___"]=d;h["OnEvent"]("MOUSE_BUTTON_RELEASED",j("1"),"mouse")end;h["G2_PRESSED"]=function()h["G2___"]=e;h["OnEvent"]("MOUSE_BUTTON_PRESSED",j("2"),"mouse")end;h["G2_RELEASED"]=function()h["G2___"]=d;h["OnEvent"]("MOUSE_BUTTON_RELEASED",j("2"),"mouse")end;h["G3_PRESSED"]=function()h["G3___"]=e;h["OnEvent"]("MOUSE_BUTTON_PRESSED",j("3"),"mouse")end;h["G3_RELEASED"]=function()h["G3___"]=d;h["OnEvent"]("MOUSE_BUTTON_RELEASED",j("3"),"mouse")end;h["G4_PRESSED"]=function()h["G4___"]=e;h["OnEvent"]("MOUSE_BUTTON_PRESSED",j("4"),"mouse")end;h["G4_RELEASED"]=function()h["G4___"]=d;h["OnEvent"]("MOUSE_BUTTON_RELEASED",j("4"),"mouse")end;h["G5_PRESSED"]=function()h["G5___"]=e;h["OnEvent"]("MOUSE_BUTTON_PRESSED",j("5"),"mouse")end;h["G5_RELEASED"]=function()h["G5___"]=d;h["OnEvent"]("MOUSE_BUTTON_RELEASED",j("5"),"mouse")end;while e do while h["IsMouseButtonPressed"](j("1"))and not h["G1___"]do h["G1_PRESSED"]()break;h["Sleep"](j("1"))end;while not h["IsMouseButtonPressed"](j("1"))and h["G1___"]do h["G1_RELEASED"]()break;h["Sleep"](j("1"))end;while h["IsMouseButtonPressed"](j("3"))and not h["G2___"]do h["G2_PRESSED"]()break;h["Sleep"](j("1"))end;while not h["IsMouseButtonPressed"](j("3"))and h["G2___"]do h["G2_RELEASED"]()break;h["Sleep"](j("1"))end;while h["IsMouseButtonPressed"](j("2"))and not h["G3___"]do h["G3_PRESSED"]()break;h["Sleep"](j("1"))end;while not h["IsMouseButtonPressed"](j("2"))and h["G3___"]do h["G3_RELEASED"]()break;h["Sleep"](j("1"))end;while h["IsMouseButtonPressed"](j("4"))and not h["G4___"]do h["G4_PRESSED"]()break;h["Sleep"](j("1"))end;while not h["IsMouseButtonPressed"](j("4"))and h["G4___"]do h["G4_RELEASED"]()break;h["Sleep"](j("1"))end;while h["IsMouseButtonPressed"](j("5"))and not h["G5___"]do h["G5_PRESSED"]()break;h["Sleep"](j("1"))end;while not h["IsMouseButtonPressed"](j("5"))and h["G5___"]do h["G5_RELEASED"]()break;h["Sleep"](j("1"))end;h["Sleep"](j("1"))end end;k=k(...)return k