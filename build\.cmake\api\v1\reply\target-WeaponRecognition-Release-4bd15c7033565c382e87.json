{"artifacts": [{"path": "bin/Release/WeaponRecognition.exe"}, {"path": "bin/Release/WeaponRecognition.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 37, "parent": 0}, {"command": 1, "file": 0, "line": 40, "parent": 0}, {"command": 2, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /O2 /Ob2 /DNDEBUG /O2 /DNDEBUG -MD"}, {"fragment": "-std:c++17"}], "includes": [{"backtrace": 3, "path": "D:/3rd_party/opencv_4.8/opencv/build/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "WeaponRecognition::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /O2 /Ob2 /DNDEBUG /O2 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "kernel32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "WeaponRecognition", "nameOnDisk": "WeaponRecognition.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/complete_main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}