# -*- coding: utf-8 -*-
"""
PUBG武器自动识别系统
替代付费识别器的免费解决方案

工作流程：
1. 检测屏幕分辨率
2. 加载对应分辨率的模板和配置·
3. 监听按键（~键触发识别，1/2键切换武器）
4. 截图并进行模板匹配
5. 输出识别结果到weapon.lua
"""

import cv2
import numpy as np
import os
import json
import time
import keyboard
import pyautogui
from concurrent.futures import ThreadPoolExecutor
import win32gui
import win32con
import win32api
from pynput import mouse  # 添加鼠标监听库

class WeaponRecognitionSystem:
    def __init__(self):
        self.base_path = os.path.dirname(os.path.abspath(__file__))
        self.nc_path = os.path.join(self.base_path, "nc")
        self.weapon_lua_path = r"C:\Temp\weapon.lua"

        # 确保C:\Temp目录存在
        temp_dir = r"C:\Temp"
        if not os.path.exists(temp_dir):
            try:
                os.makedirs(temp_dir)
                print(f"✅ 创建目录: {temp_dir}")
            except Exception as e:
                print(f"❌ 创建目录失败: {e}")
        
        # 系统状态
        self.current_resolution = None
        self.config = None
        self.templates = {}
        self.current_weapon_slot = 1  # 1=主武器, 2=副武器
        self.is_running = False

        # 滚轮缩放相关变量
        self.scope_zoom = 1.6  # 默认最大缩放值
        self.max_zoom = 1.9  # 最大缩放值
        self.min_zoom = 0.9  # 最小缩放值
        self.zoom_step = 0.06  # 每次滚动的增量
        self.right_button_pressed = False  # 右键状态标记
        self.mouse_listener = None  # 鼠标监听器
        
        # 识别结果 - 分别存储两把武器的配置
        self.rifle_config = {
            "weapon_name": "",
            "muzzles": "",
            "grips": "",
            "scopes": "",
            "stocks": "",
            "poses": "None",
            "scope_zoom": "1.0",
            "car": "None"
        }

        self.sniper_config = {
            "weapon_name": "",
            "muzzles": "",
            "grips": "",
            "scopes": "",
            "stocks": "",
            "poses": "None",
            "scope_zoom": "1.0",
            "car": "None"
        }
        
        print("🎯 PUBG武器自动识别系统启动")
        print("=" * 50)

    def on_scroll(self, x, y, dx, dy):
        """处理鼠标滚轮事件"""
        if self.right_button_pressed:  # 仅当右键按下时才处理滚轮事件
            current_config = self.rifle_config if self.current_weapon_slot == 1 else self.sniper_config
            current_scope = current_config.get("scopes", "none")

            if current_scope in ["x6", "x8"]:
                if dy > 0:  # 向上滚动，放大
                    self.scope_zoom = min(self.scope_zoom + self.zoom_step, self.max_zoom)
                elif dy < 0:  # 向下滚动，缩小
                    self.scope_zoom = max(self.scope_zoom - self.zoom_step, self.min_zoom)

                # 更新当前武器配置中的scope_zoom
                current_config["scope_zoom"] = str(round(self.scope_zoom, 2))
                print(f"🔍 {current_scope}倍镜缩放调整为: {self.scope_zoom:.2f}")

                # 滚轮滚动时也实时检测姿势和载具状态
                self.detect_realtime_status()

    def on_click(self, x, y, button, pressed):
        """处理鼠标点击事件"""
        if button == mouse.Button.right:
            self.right_button_pressed = pressed  # 更新右键按下状态

            # 右键按下时（开镜时）实时检测姿势和载具状态
            if pressed and self.is_running:
                print("🎯 开镜检测 - 实时更新姿势和载具状态")
                self.detect_realtime_status()

    def handle_weapon_change(self):
        """处理武器切换时的缩放重置"""
        current_config = self.rifle_config if self.current_weapon_slot == 1 else self.sniper_config
        current_scope = current_config.get("scopes", "none")

        if current_scope in ["x6", "x8"]:
            self.scope_zoom = 1.6  # 高倍镜重置为最大缩放值
            current_config["scope_zoom"] = "1.6"
        else:
            self.scope_zoom = 1.0  # 其他倍镜重置为1.0
            current_config["scope_zoom"] = "1.0"

    def detect_realtime_status(self):
        """实时检测姿势和载具状态"""
        try:
            if not self.config or not self.templates:
                return

            regions = self.config.get("regions", {})
            current_config = self.rifle_config if self.current_weapon_slot == 1 else self.sniper_config

            # 检测姿势
            if "poses" in regions:
                pose_result = self.recognize_region("poses", regions["poses"])
                if pose_result:
                    current_config["poses"] = pose_result
                    print(f"🏃 姿势检测: {pose_result}")

            # 检测载具状态
            if "car" in regions:
                car_result = self.recognize_region("car", regions["car"])
                car_status = "car" if car_result else "None"
                current_config["car"] = car_status
                print(f"🚗 载具状态: {car_status}")

            # 实时写入lua文件
            self.write_weapon_config(current_config)

        except Exception as e:
            print(f"❌ 实时状态检测出错: {e}")
    
    def detect_screen_resolution(self):
        """检测屏幕分辨率"""
        try:
            # 获取主显示器分辨率
            width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
            height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
            self.current_resolution = (width, height)
            print(f"✅ 检测到屏幕分辨率: {width}x{height}")
            return True
        except Exception as e:
            print(f"❌ 分辨率检测失败: {e}")
            return False
    
    def load_config(self):
        """加载对应分辨率的配置文件"""
        if not self.current_resolution:
            return False
        
        width, height = self.current_resolution
        resolution_folder = f"{width}x{height}"
        config_path = os.path.join(self.nc_path, resolution_folder, "config.json")
        
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                print(f"✅ 加载配置文件: {resolution_folder}")
                return True
            else:
                # 尝试使用最接近的分辨率
                available_resolutions = self.get_available_resolutions()
                closest = self.find_closest_resolution(width, height, available_resolutions)
                if closest:
                    config_path = os.path.join(self.nc_path, closest, "config.json")
                    with open(config_path, 'r', encoding='utf-8') as f:
                        self.config = json.load(f)
                    print(f"✅ 使用最接近的分辨率配置: {closest}")
                    return True
                else:
                    print(f"❌ 未找到分辨率 {resolution_folder} 的配置文件")
                    return False
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False
    
    def get_available_resolutions(self):
        """获取可用的分辨率配置"""
        available = []
        try:
            for item in os.listdir(self.nc_path):
                item_path = os.path.join(self.nc_path, item)
                if os.path.isdir(item_path) and 'x' in item:
                    config_file = os.path.join(item_path, "config.json")
                    if os.path.exists(config_file):
                        available.append(item)
        except:
            pass
        return available
    
    def find_closest_resolution(self, target_width, target_height, available_resolutions):
        """找到最接近的分辨率"""
        if not available_resolutions:
            return None
        
        min_distance = float('inf')
        closest = None
        
        for res in available_resolutions:
            try:
                w, h = map(int, res.split('x'))
                distance = ((target_width - w) ** 2 + (target_height - h) ** 2) ** 0.5
                if distance < min_distance:
                    min_distance = distance
                    closest = res
            except:
                continue
        
        return closest
    
    def load_templates(self):
        """加载武器和配件模板"""
        if not self.config:
            return False
        
        width, height = self.current_resolution
        resolution_folder = f"{width}x{height}"
        
        # 如果当前分辨率不存在，使用最接近的
        if not os.path.exists(os.path.join(self.nc_path, resolution_folder)):
            available_resolutions = self.get_available_resolutions()
            resolution_folder = self.find_closest_resolution(width, height, available_resolutions)
        
        template_base = os.path.join(self.nc_path, resolution_folder, "weapon_templates")
        
        template_dirs = {
            "poses": os.path.join(template_base, "poses"),
            "weapons": os.path.join(template_base, "weapons"),
            "muzzles": os.path.join(template_base, "muzzles"),
            "grips": os.path.join(template_base, "grips"),
            "scopes": os.path.join(template_base, "scopes"),
            "stocks": os.path.join(template_base, "stocks"),
        }
        
        print("📁 开始加载模板文件...")
        total_templates = 0
        
        for category, path in template_dirs.items():
            self.templates[category] = {}
            if os.path.exists(path):
                try:
                    for file in os.listdir(path):
                        if file.lower().endswith('.png'):
                            template_name = file[:-4]  # 去掉.png扩展名
                            template_path = os.path.join(path, file)
                            template = cv2.imread(template_path, 0)  # 灰度读取
                            if template is not None:
                                self.templates[category][template_name] = template
                                total_templates += 1
                    print(f"  ✅ {category}: {len(self.templates[category])} 个模板")
                except Exception as e:
                    print(f"  ❌ {category} 加载失败: {e}")
            else:
                print(f"  ⚠️ {category} 文件夹不存在: {path}")
        
        print(f"✅ 模板加载完成！总共 {total_templates} 个模板文件")
        return total_templates > 0
    
    def setup_hotkeys(self):
        """设置热键监听"""
        try:
            # ~键触发识别
            keyboard.add_hotkey('`', self.trigger_recognition)
            # 1键切换到主武器
            keyboard.add_hotkey('1', lambda: self.switch_weapon(1))
            # 2键切换到副武器  
            keyboard.add_hotkey('2', lambda: self.switch_weapon(2))
            print("✅ 热键设置完成:")
            print("  ` (波浪号键) - 触发武器识别")
            print("  1 键 - 切换到主武器")
            print("  2 键 - 切换到副武器")
            return True
        except Exception as e:
            print(f"❌ 热键设置失败: {e}")
            return False

    def setup_mouse_listener(self):
        """设置鼠标监听器"""
        try:
            self.mouse_listener = mouse.Listener(
                on_click=self.on_click,
                on_scroll=self.on_scroll
            )
            self.mouse_listener.start()
            print("✅ 鼠标监听器设置完成:")
            print("  右键按下 - 实时检测姿势和载具状态")
            print("  右键+滚轮 - 调整高倍镜缩放 (仅x6、x8倍镜)")
            print("  缩放范围: 1.0 - 1.6")
            return True
        except Exception as e:
            print(f"❌ 鼠标监听器设置失败: {e}")
            return False
    
    def switch_weapon(self, slot):
        """切换武器槽位并输出对应配置"""
        self.current_weapon_slot = slot

        # 处理武器切换时的缩放重置
        self.handle_weapon_change()

        if slot == 1:
            # 输出步枪配置
            print("🔄 切换到步枪 (槽位 1)")
            if self.rifle_config.get("weapon_name"):
                self.write_weapon_config(self.rifle_config)
                print("✅ 已输出步枪配置到 C:\\Temp\\weapon.lua")
                print(f"   武器: {self.rifle_config.get('weapon_name')}")
                print(f"   配件: 枪口={self.rifle_config.get('muzzles', '')}, 握把={self.rifle_config.get('grips', '')}, 瞄准镜={self.rifle_config.get('scopes', '')}")
                print(f"   枪托={self.rifle_config.get('stocks', '')}, 姿势={self.rifle_config.get('poses', '')}, 倍镜={self.rifle_config.get('scope_zoom', '')}")
                print(f"   载具={self.rifle_config.get('car', 'None')}")
            else:
                print("⚠️ 步枪配置为空，请先按 ` 键进行识别")

        elif slot == 2:
            # 输出狙击枪配置
            print("🔄 切换到狙击枪 (槽位 2)")
            if self.sniper_config.get("weapon_name"):
                self.write_weapon_config(self.sniper_config)
                print("✅ 已输出狙击枪配置到 C:\\Temp\\weapon.lua")
                print(f"   武器: {self.sniper_config.get('weapon_name')}")
                print(f"   配件: 枪口={self.sniper_config.get('muzzles', '')}, 握把={self.sniper_config.get('grips', '')}, 瞄准镜={self.sniper_config.get('scopes', '')}")
                print(f"   枪托={self.sniper_config.get('stocks', '')}, 姿势={self.sniper_config.get('poses', '')}, 倍镜={self.sniper_config.get('scope_zoom', '')}")
                print(f"   载具={self.sniper_config.get('car', 'None')}")
            else:
                print("⚠️ 狙击枪配置为空，请先按 ` 键进行识别")
    
    def trigger_recognition(self):
        """触发武器识别 - 同时识别两把武器"""
        if not self.is_running:
            print("⚠️ 系统未完全初始化，无法进行识别")
            return

        print("🎯 开始识别武器配置...")
        print("📸 同时识别步枪和狙击枪配置...")

        try:
            # 同时识别两把武器
            rifle_result = self.recognize_weapon_by_type("rifle")
            sniper_result = self.recognize_weapon_by_type("sniper")

            if rifle_result:
                self.rifle_config = rifle_result
                print("✅ 步枪配置识别完成")
                print(f"   武器: {rifle_result.get('weapon_name', '未识别')}")
                print(f"   配件: 枪口={rifle_result.get('muzzles', '')}, 握把={rifle_result.get('grips', '')}, 瞄准镜={rifle_result.get('scopes', '')}")
                print(f"   枪托={rifle_result.get('stocks', '')}, 姿势={rifle_result.get('poses', '')}, 倍镜={rifle_result.get('scope_zoom', '')}")
                print(f"   载具={rifle_result.get('car', 'None')}")

            if sniper_result:
                self.sniper_config = sniper_result
                print("✅ 狙击枪配置识别完成")
                print(f"   武器: {sniper_result.get('weapon_name', '未识别')}")
                print(f"   配件: 枪口={sniper_result.get('muzzles', '')}, 握把={sniper_result.get('grips', '')}, 瞄准镜={sniper_result.get('scopes', '')}")
                print(f"   枪托={sniper_result.get('stocks', '')}, 姿势={sniper_result.get('poses', '')}, 倍镜={sniper_result.get('scope_zoom', '')}")
                print(f"   载具={sniper_result.get('car', 'None')}")

            print("💡 按 1 键输出步枪配置，按 2 键输出狙击枪配置")

        except Exception as e:
            print(f"❌ 识别过程出错: {e}")
    
    def initialize(self):
        """初始化系统"""
        print("🔧 正在初始化系统...")
        
        # 1. 检测分辨率
        if not self.detect_screen_resolution():
            return False
        
        # 2. 加载配置
        if not self.load_config():
            return False
        
        # 3. 加载模板
        if not self.load_templates():
            return False
        
        # 4. 设置热键
        if not self.setup_hotkeys():
            return False

        # 5. 启动鼠标监听器
        if not self.setup_mouse_listener():
            return False

        self.is_running = True
        print("🎉 系统初始化完成！")
        print("\n使用说明:")
        print("- 按 ` 键同时识别两把武器的配置")
        print("- 按 1 键输出步枪配置到 C:\\Temp\\weapon.lua")
        print("- 按 2 键输出狙击枪配置到 C:\\Temp\\weapon.lua")
        print("- 右键+滚轮调整高倍镜缩放 (仅x6、x8倍镜)")
        print("- 按 Ctrl+C 退出程序")
        print("\n工作流程:")
        print("1. 进入游戏，确保武器界面可见")
        print("2. 按 ` 键识别当前两把武器的配置")
        print("3. 按 1 或 2 键选择要使用的武器配置")
        print("4. 对于x6/x8倍镜，可按住右键+滚轮调整缩放")
        print("5. 罗技脚本会自动读取配置进行压枪")
        return True

    def capture_screen_region(self, region):
        """截取屏幕指定区域"""
        try:
            x, y, w, h = region
            screenshot = pyautogui.screenshot(region=(x, y, w, h))
            # 转换为OpenCV格式
            frame = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            return gray_frame
        except Exception as e:
            print(f"❌ 截图失败: {e}")
            return None

    def match_template(self, image, templates, threshold=0.7):
        """模板匹配"""
        if not templates or image is None:
            return ""

        best_match = ""
        best_score = 0

        for template_name, template in templates.items():
            try:
                # 模板匹配
                result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, _ = cv2.minMaxLoc(result)

                if max_val > best_score and max_val > threshold:
                    best_score = max_val
                    best_match = template_name
            except Exception as e:
                continue

        return best_match if best_score > threshold else ""

    def recognize_weapon_by_type(self, weapon_type):
        """根据武器类型识别配置"""
        if not self.config or not self.templates:
            return None

        regions = self.config.get("regions", {})
        result = {
            "weapon_name": "",
            "muzzles": "",
            "grips": "",
            "scopes": "",
            "stocks": "",
            "poses": "None",
            "scope_zoom": "1.0",
            "car": "None"
        }

        # 根据武器类型选择对应的区域
        region_mapping = {
            "weapon_name": f"weapon_name_{weapon_type}",
            "muzzles": f"muzzles_{weapon_type}",
            "grips": f"grips_{weapon_type}",
            "scopes": f"scopes_{weapon_type}",
            "stocks": f"stocks_{weapon_type}",
            "poses": "poses"  # 姿态是共用的
        }

        # 识别各个配件
        for config_key, region_key in region_mapping.items():
            if region_key in regions:
                region_coords = regions[region_key]
                try:
                    if config_key == "weapon_name":
                        recognition_result = self.recognize_region("weapons", region_coords)
                    elif config_key == "poses":
                        recognition_result = self.recognize_region("poses", region_coords)
                    else:
                        # 其他配件
                        template_category = config_key if config_key != "muzzles" else "muzzles"
                        recognition_result = self.recognize_region(template_category, region_coords)

                    if recognition_result:
                        result[config_key] = recognition_result

                except Exception as e:
                    print(f"  ❌ {config_key}: 识别出错 - {e}")

        # 根据瞄准镜类型设置倍数 - 只有x6和x8可以缩放，其他都是1.0
        scope_zoom_map = {
            "None": "1.0",
            "reddot": "1.0",
            "quanxi": "1.0",
            "x2": "1.0",
            "x3": "1.0",
            "x4": "1.0",
            "x6": "1.6",  # x6倍镜默认1.6（最大缩放），可通过滚轮缩小到1.0
            "x8": "1.6"   # x8倍镜默认1.6（最大缩放），可通过滚轮缩小到1.0
        }

        if result["scopes"] in scope_zoom_map:
            result["scope_zoom"] = scope_zoom_map[result["scopes"]]

        return result

    def recognize_weapon(self):
        """执行武器识别"""
        if not self.config or not self.templates:
            return None

        regions = self.config.get("regions", {})
        result = {
            "weapon_name": "",
            "muzzles": "",
            "grips": "",
            "scopes": "",
            "stocks": "",
            "poses": "None",
            "scope_zoom": "1.0",
            "car": "None"
        }

        # 简化版识别 - 直接识别所有区域
        for region_name, region_coords in regions.items():
            try:
                # 跳过不需要识别的区域
                if region_name.lower() in ["cursor", "bag", "car", "shoot"]:
                    continue

                if "weapon" in region_name.lower():
                    recognition_result = self.recognize_region("weapons", region_coords)
                    if recognition_result:
                        result["weapon_name"] = recognition_result
                        print(f"  ✅ 武器: {recognition_result}")
                elif "muzzle" in region_name.lower():
                    recognition_result = self.recognize_region("muzzles", region_coords)
                    if recognition_result:
                        result["muzzles"] = recognition_result
                        print(f"  ✅ 枪口: {recognition_result}")
                elif "grip" in region_name.lower():
                    recognition_result = self.recognize_region("grips", region_coords)
                    if recognition_result:
                        result["grips"] = recognition_result
                        print(f"  ✅ 握把: {recognition_result}")
                elif "scope" in region_name.lower():
                    recognition_result = self.recognize_region("scopes", region_coords)
                    if recognition_result:
                        result["scopes"] = recognition_result
                        print(f"  ✅ 瞄准镜: {recognition_result}")
                elif "stock" in region_name.lower():
                    recognition_result = self.recognize_region("stocks", region_coords)
                    if recognition_result:
                        result["stocks"] = recognition_result
                        print(f"  ✅ 枪托: {recognition_result}")
                elif "pose" in region_name.lower():
                    recognition_result = self.recognize_region("poses", region_coords)
                    if recognition_result:
                        result["poses"] = recognition_result
                        print(f"  ✅ 姿态: {recognition_result}")
                else:
                    # 对于其他未知区域，跳过而不显示警告
                    print(f"  ⏭️ 跳过区域: {region_name}")
            except Exception as e:
                print(f"  ❌ {region_name}: 识别出错 - {e}")

        # 根据瞄准镜类型设置倍数 - 只有x6和x8可以缩放，其他都是1.0
        scope_zoom_map = {
            "None": "1.0",
            "reddot": "1.0",
            "quanxi": "1.0",
            "x2": "1.0",
            "x3": "1.0",
            "x4": "1.0",
            "x6": "1.6",  # x6倍镜默认1.6（最大缩放），可通过滚轮缩小到1.0
            "x8": "1.6"   # x8倍镜默认1.6（最大缩放），可通过滚轮缩小到1.0
        }

        if result["scopes"] in scope_zoom_map:
            result["scope_zoom"] = scope_zoom_map[result["scopes"]]

        # 保存识别结果
        self.last_recognition_result = result.copy()

        return result

    def recognize_region(self, template_category, region_coords):
        """识别单个区域"""
        try:
            print(f"🔍 正在识别 {template_category} 区域: {region_coords}")

            # 截取区域图像
            image = self.capture_screen_region(region_coords)
            if image is None:
                print(f"❌ {template_category}: 截图失败")
                return ""

            # 获取对应类别的模板
            templates = self.templates.get(template_category, {})
            if not templates:
                print(f"❌ {template_category}: 没有找到模板文件")
                return ""

            print(f"📁 {template_category}: 找到 {len(templates)} 个模板")

            # 进行模板匹配
            result = self.match_template(image, templates, threshold=0.6)
            if result:
                print(f"✅ {template_category}: 识别到 {result}")
            else:
                print(f"⚠️ {template_category}: 未识别到匹配项")
            return result
        except Exception as e:
            print(f"❌ {template_category} 区域识别出错: {e}")
            return ""

    def write_weapon_config(self, config):
        """写入武器配置到weapon.lua文件"""
        try:
            with open(self.weapon_lua_path, "w", encoding="utf-8") as f:
                f.write(f'weapon_name = "{config.get("weapon_name", "")}"\n')
                f.write(f'muzzles = "{config.get("muzzles", "")}"\n')
                f.write('muzzle="None"\n')  # 固定为None（授权限制）
                f.write(f'grips = "{config.get("grips", "")}"\n')
                f.write(f'scopes = "{config.get("scopes", "")}"\n')
                f.write(f'stocks = "{config.get("stocks", "")}"\n')
                f.write(f'poses = "{config.get("poses", "None")}"\n')
                f.write(f'scope_zoom = "{config.get("scope_zoom", "1.0")}"\n')
                f.write('bag = "none"\n')
                f.write(f'car = "{config.get("car", "None")}"\n')
                f.write('shoot = "None"\n')
                f.write('cursor_x = 0\n')

            print(f"📝 配置已写入: {self.weapon_lua_path}")
            return True
        except Exception as e:
            print(f"❌ 写入配置文件失败: {e}")
            return False

    def run(self):
        """运行主程序"""
        if not self.initialize():
            print("❌ 系统初始化失败")
            return

        try:
            print("\n🎮 系统正在运行，等待按键操作...")
            # 保持程序运行
            while True:
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n👋 程序退出")
            if self.mouse_listener:
                self.mouse_listener.stop()
        except Exception as e:
            print(f"❌ 程序运行出错: {e}")
            if self.mouse_listener:
                self.mouse_listener.stop()

def main():
    """主函数"""
    system = WeaponRecognitionSystem()
    system.run()

if __name__ == "__main__":
    main()
