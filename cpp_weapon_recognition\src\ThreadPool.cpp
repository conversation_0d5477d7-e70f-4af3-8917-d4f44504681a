#include "ThreadPool.h"
#include <iostream>

namespace WeaponRecognition {

ThreadPool::ThreadPool(size_t num_threads) : stop_(false) {
    for (size_t i = 0; i < num_threads; ++i) {
        workers_.emplace_back([this] {
            for (;;) {
                std::function<void()> task;
                
                {
                    std::unique_lock<std::mutex> lock(queue_mutex_);
                    condition_.wait(lock, [this] { return stop_ || !tasks_.empty(); });
                    
                    if (stop_ && tasks_.empty()) {
                        return;
                    }
                    
                    task = std::move(tasks_.front());
                    tasks_.pop();
                }
                
                try {
                    task();
                } catch (const std::exception& e) {
                    std::cerr << "❌ 线程池任务执行异常: " << e.what() << std::endl;
                } catch (...) {
                    std::cerr << "❌ 线程池任务执行未知异常" << std::endl;
                }
            }
        });
    }
    
    std::cout << "✅ 线程池已创建，工作线程数: " << num_threads << std::endl;
}

ThreadPool::~ThreadPool() {
    shutdown();
}

void ThreadPool::shutdown() {
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        stop_ = true;
    }
    
    condition_.notify_all();
    
    for (std::thread& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    workers_.clear();
    std::cout << "✅ 线程池已关闭" << std::endl;
}

} // namespace WeaponRecognition
