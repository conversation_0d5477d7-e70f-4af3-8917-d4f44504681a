cmake_minimum_required(VERSION 3.16)
project(WeaponRecognition)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /O2")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
endif()

# 设置OpenCV路径
set(OpenCV_DIR "D:/3rd_party/opencv_4.8/opencv/build")
set(OpenCV_INCLUDE_DIRS "${OpenCV_DIR}/include")
set(OpenCV_LIB_DIR "${OpenCV_DIR}/x64/vc16/lib")

# 查找OpenCV库文件
file(GLOB OpenCV_LIBS "${OpenCV_LIB_DIR}/opencv_world*.lib")
if(NOT OpenCV_LIBS)
    file(GLOB OpenCV_LIBS "${OpenCV_LIB_DIR}/opencv_*.lib")
endif()

message(STATUS "OpenCV路径: ${OpenCV_DIR}")
message(STATUS "OpenCV包含目录: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV库: ${OpenCV_LIBS}")

# 包含目录
include_directories(${OpenCV_INCLUDE_DIRS})

# 源文件
set(SOURCES
    src/complete_main.cpp
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    ${OpenCV_LIBS}
    user32
    gdi32
    kernel32
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
