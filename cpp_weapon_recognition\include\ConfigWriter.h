#pragma once

#include "Types.h"
#include <string>
#include <mutex>

namespace WeaponRecognition {

class ConfigWriter {
public:
    ConfigWriter() = default;
    ~ConfigWriter() = default;

    // 初始化
    bool initialize(const std::string& output_path);
    
    // 写入武器配置
    bool writeWeaponConfig(const WeaponConfig& config);
    
    // 原子写入（避免文件冲突）
    bool atomicWrite(const std::string& content);

private:
    std::string output_path_;
    std::mutex write_mutex_;
    
    // 内部方法
    std::string formatConfig(const WeaponConfig& config);
    bool ensureDirectoryExists(const std::string& file_path);
};

} // namespace WeaponRecognition
