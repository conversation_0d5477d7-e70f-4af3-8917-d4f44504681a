#pragma once

#include "Types.h"
#include <functional>
#include <unordered_map>
#include <windows.h>

namespace WeaponRecognition {

class HotkeyManager {
public:
    using HotkeyCallback = std::function<void(HotkeyType)>;

    HotkeyManager();
    ~HotkeyManager();

    // 设置回调函数
    void setCallback(HotkeyCallback callback) { callback_ = callback; }
    
    // 注册热键
    bool registerHotkey(HotkeyType type, int vk_code);
    
    // 注销热键
    bool unregisterHotkey(HotkeyType type);
    
    // 注销所有热键
    void unregisterAllHotkeys();

private:
    HotkeyCallback callback_;
    std::unordered_map<int, HotkeyType> hotkey_map_;
    static HotkeyManager* instance_;
    
    // Windows消息处理
    static LRESULT CALLBACK hotkeyProc(int nCode, WPARAM wParam, LPARAM lParam);
    HHOOK hook_handle_;
    
    // 内部方法
    void handleHotkey(int vk_code);
};

} // namespace WeaponRecognition
