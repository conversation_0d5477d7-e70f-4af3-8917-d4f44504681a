#include "WeaponRecognitionSystem.h"
#include "Utils.h"
#include <iostream>
#include <thread>
#include <chrono>

namespace WeaponRecognition {

WeaponRecognitionSystem::WeaponRecognitionSystem() {
    // 初始化组件
    config_manager_ = std::make_unique<ConfigManager>();
    template_manager_ = std::make_unique<TemplateManager>();
    capture_engine_ = std::make_unique<ScreenCaptureEngine>();
    recognition_engine_ = std::make_unique<RecognitionEngine>(template_manager_);
    hotkey_manager_ = std::make_unique<HotkeyManager>();
    mouse_listener_ = std::make_unique<MouseListener>();
    config_writer_ = std::make_unique<ConfigWriter>();
    thread_pool_ = std::make_unique<ThreadPool>(4); // 4个工作线程

    // 初始化武器配置
    rifle_config_.reset();
    sniper_config_.reset();

    logInfo("武器识别系统已创建");
}

WeaponRecognitionSystem::~WeaponRecognitionSystem() {
    stop();
    cleanup();
    logInfo("武器识别系统已销毁");
}

bool WeaponRecognitionSystem::initialize() {
    logInfo("开始初始化系统...");
    
    if (!initializeComponents()) {
        logError("组件初始化失败");
        return false;
    }
    
    if (!loadConfiguration()) {
        logError("配置加载失败");
        return false;
    }
    
    if (!loadTemplates()) {
        logError("模板加载失败");
        return false;
    }
    
    if (!setupHotkeys()) {
        logError("热键设置失败");
        return false;
    }
    
    if (!setupMouseListener()) {
        logError("鼠标监听设置失败");
        return false;
    }
    
    is_initialized_ = true;
    logInfo("系统初始化完成");
    return true;
}

void WeaponRecognitionSystem::run() {
    if (!is_initialized_) {
        logError("系统未初始化，无法运行");
        return;
    }
    
    is_running_ = true;
    logInfo("系统开始运行");
    
    // 主循环 - 极简设计，降低CPU占用
    while (is_running_) {
        // 检查性能统计
        if (performance_stats_.total_recognitions % 100 == 0 && 
            performance_stats_.total_recognitions > 0) {
            logPerformance("系统运行统计", 
                          performance_stats_.avg_recognition_time);
        }
        
        // 低频率睡眠，减少CPU占用
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    
    logInfo("系统运行结束");
}

void WeaponRecognitionSystem::stop() {
    if (is_running_) {
        is_running_ = false;
        logInfo("正在停止系统...");
        
        // 等待所有任务完成（简化版）
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        logInfo("系统已停止");
    }
}

bool WeaponRecognitionSystem::initializeComponents() {
    // 初始化截图引擎
    if (!capture_engine_->initialize()) {
        return false;
    }

    // 设置优化级别为速度优先
    capture_engine_->setOptimizationLevel(2);

    // 初始化配置写入器
    if (!config_writer_->initialize("C:\\Temp\\weapon.lua")) {
        return false;
    }

    return true;
}

bool WeaponRecognitionSystem::loadConfiguration() {
    auto resolution = capture_engine_->getScreenResolution();
    return config_manager_->loadConfig(resolution.first, resolution.second);
}

bool WeaponRecognitionSystem::loadTemplates() {
    const auto& config = config_manager_->getSystemConfig();
    return template_manager_->loadTemplates(config.template_path);
}

bool WeaponRecognitionSystem::setupHotkeys() {
    // 设置热键回调
    hotkey_manager_->setCallback([this](HotkeyType type) {
        onHotkeyPressed(type);
    });

    // 注册热键
    return hotkey_manager_->registerHotkey(HotkeyType::TRIGGER_RECOGNITION, VK_OEM_3) &&  // ` 键
           hotkey_manager_->registerHotkey(HotkeyType::SWITCH_RIFLE, '1') &&
           hotkey_manager_->registerHotkey(HotkeyType::SWITCH_SNIPER, '2') &&
           hotkey_manager_->registerHotkey(HotkeyType::EXIT_PROGRAM, VK_ESCAPE);
}

bool WeaponRecognitionSystem::setupMouseListener() {
    // 设置鼠标事件回调
    mouse_listener_->setCallback([this](const MouseEvent& event) {
        onMouseEvent(event);
    });

    return mouse_listener_->startListening();
}

void WeaponRecognitionSystem::onHotkeyPressed(HotkeyType type) {
    switch (type) {
    case HotkeyType::TRIGGER_RECOGNITION:
        // 在线程池中异步执行识别
        thread_pool_->enqueue([this]() {
            triggerRecognition();
        });
        break;

    case HotkeyType::SWITCH_RIFLE:
        thread_pool_->enqueue([this]() {
            fastSwitchWeapon(WeaponType::RIFLE);
        });
        break;

    case HotkeyType::SWITCH_SNIPER:
        thread_pool_->enqueue([this]() {
            fastSwitchWeapon(WeaponType::SNIPER);
        });
        break;

    case HotkeyType::EXIT_PROGRAM:
        stop();
        break;
    }
}

void WeaponRecognitionSystem::onMouseEvent(const MouseEvent& event) {
    switch (event.type) {
    case MouseEventType::RIGHT_DOWN:
        right_button_pressed_ = true;
        // 开镜时实时更新状态
        thread_pool_->enqueue([this]() {
            updateRealtimeStatus();
        });
        break;

    case MouseEventType::RIGHT_UP:
        right_button_pressed_ = false;
        break;

    case MouseEventType::SCROLL_UP:
    case MouseEventType::SCROLL_DOWN:
        if (right_button_pressed_) {
            int delta = (event.type == MouseEventType::SCROLL_UP) ? 1 : -1;
            handleScopeZoom(delta);
            // 滚轮时也更新状态
            thread_pool_->enqueue([this]() {
                updateRealtimeStatus();
            });
        }
        break;

    default:
        break;
    }
}

void WeaponRecognitionSystem::logInfo(const std::string& message) {
    std::cout << "[INFO] " << Utils::getCurrentTimeString() << " " << message << std::endl;
}

void WeaponRecognitionSystem::logError(const std::string& message) {
    std::cerr << "[ERROR] " << Utils::getCurrentTimeString() << " " << message << std::endl;
}

void WeaponRecognitionSystem::logPerformance(const std::string& operation, double time_ms) {
    std::cout << "[PERF] " << operation << ": " << time_ms << "ms" << std::endl;
}

void WeaponRecognitionSystem::triggerRecognition() {
    logInfo("🎯 开始武器识别...");

    const auto& regions = config_manager_->getRegions();

    // 截取所有需要的区域
    std::unordered_map<std::string, cv::Mat> region_images;

    for (const auto& [region_name, region_rect] : regions) {
        cv::Mat image = capture_engine_->captureRegion(region_rect);
        if (!image.empty()) {
            region_images[region_name] = image;
        }
    }

    // 并行识别步枪和狙击枪
    auto rifle_future = std::async(std::launch::async, [this, &region_images]() {
        return recognition_engine_->recognizeWeaponConfig(region_images, WeaponType::RIFLE);
    });

    auto sniper_future = std::async(std::launch::async, [this, &region_images]() {
        return recognition_engine_->recognizeWeaponConfig(region_images, WeaponType::SNIPER);
    });

    // 获取识别结果
    rifle_config_ = rifle_future.get();
    sniper_config_ = sniper_future.get();

    // 输出识别结果
    logInfo("✅ 步枪配置识别完成");
    logInfo("   武器: " + rifle_config_.weapon_name);
    logInfo("   配件: 枪口=" + rifle_config_.muzzles + ", 握把=" + rifle_config_.grips + ", 瞄准镜=" + rifle_config_.scopes);
    logInfo("   枪托=" + rifle_config_.stocks + ", 姿势=" + rifle_config_.poses + ", 倍镜=" + std::to_string(rifle_config_.scope_zoom));
    logInfo("   载具=" + rifle_config_.car);

    logInfo("✅ 狙击枪配置识别完成");
    logInfo("   武器: " + sniper_config_.weapon_name);
    logInfo("   配件: 枪口=" + sniper_config_.muzzles + ", 握把=" + sniper_config_.grips + ", 瞄准镜=" + sniper_config_.scopes);
    logInfo("   枪托=" + sniper_config_.stocks + ", 姿势=" + sniper_config_.poses + ", 倍镜=" + std::to_string(sniper_config_.scope_zoom));
    logInfo("   载具=" + sniper_config_.car);
}

void WeaponRecognitionSystem::fastSwitchWeapon(WeaponType type) {
    current_weapon_type_ = type;

    WeaponConfig& config = (type == WeaponType::RIFLE) ? rifle_config_ : sniper_config_;
    std::string weapon_name = (type == WeaponType::RIFLE) ? "步枪" : "狙击枪";

    logInfo("🔄 切换到" + weapon_name + " (槽位 " + std::to_string(static_cast<int>(type) + 1) + ")");

    if (!config.weapon_name.empty()) {
        // 处理武器切换时的缩放重置
        handleWeaponChange();

        // 立即写入配置
        config_writer_->writeWeaponConfig(config);
        logInfo("✅ 已输出" + weapon_name + "配置到 C:\\Temp\\weapon.lua");

        // 快速更新姿势和载具状态
        updateRealtimeStatus();
    } else {
        logInfo("⚠️ " + weapon_name + "配置为空，正在快速识别...");
        quickRecognizeWeapon(type);
    }
}

void WeaponRecognitionSystem::quickRecognizeWeapon(WeaponType type) {
    logInfo("⚡ 快速识别" + std::string(type == WeaponType::RIFLE ? "步枪" : "狙击枪") + "配置中...");

    const auto& regions = config_manager_->getRegions();
    std::unordered_map<std::string, cv::Mat> region_images;

    // 只截取当前武器类型相关的区域
    std::vector<std::string> target_regions;
    if (type == WeaponType::RIFLE) {
        target_regions = {"weapon_name_rifle", "muzzles_rifle", "grips_rifle", "scopes_rifle", "stocks_rifle"};
    } else {
        target_regions = {"weapon_name_sniper", "muzzles_sniper", "grips_sniper", "scopes_sniper", "stocks_sniper"};
    }
    target_regions.insert(target_regions.end(), {"poses", "car"});

    for (const auto& region_name : target_regions) {
        if (regions.count(region_name)) {
            cv::Mat image = capture_engine_->captureRegion(regions.at(region_name));
            if (!image.empty()) {
                region_images[region_name] = image;
            }
        }
    }

    // 快速识别
    WeaponConfig result = recognition_engine_->recognizeWeaponConfig(region_images, type);

    if (!result.weapon_name.empty()) {
        if (type == WeaponType::RIFLE) {
            rifle_config_ = result;
        } else {
            sniper_config_ = result;
        }

        config_writer_->writeWeaponConfig(result);
        logInfo("✅ " + std::string(type == WeaponType::RIFLE ? "步枪" : "狙击枪") + "配置快速识别完成");
    } else {
        logInfo("❌ 快速识别失败，请确保武器界面可见");
    }
}

void WeaponRecognitionSystem::updateRealtimeStatus() {
    const auto& regions = config_manager_->getRegions();
    WeaponConfig& current_config = getCurrentConfig();

    // 快速检测姿势
    if (regions.count("poses")) {
        cv::Mat pose_image = capture_engine_->captureRegion(regions.at("poses"));
        if (!pose_image.empty()) {
            std::string pose_result = recognition_engine_->recognizePose(pose_image);
            if (!pose_result.empty()) {
                current_config.poses = pose_result;
                logInfo("🏃 姿势检测: " + pose_result);
            }
        }
    }

    // 快速检测载具状态
    if (regions.count("car")) {
        cv::Mat car_image = capture_engine_->captureRegion(regions.at("car"));
        if (!car_image.empty()) {
            bool in_vehicle = recognition_engine_->recognizeVehicleStatus(car_image);
            current_config.car = in_vehicle ? "car" : "None";
            logInfo("🚗 载具状态: " + current_config.car);
        }
    }

    // 实时写入lua文件
    config_writer_->writeWeaponConfig(current_config);
}

void WeaponRecognitionSystem::handleScopeZoom(int delta) {
    WeaponConfig& current_config = getCurrentConfig();

    if (current_config.scopes == "x6" || current_config.scopes == "x8") {
        const auto& system_config = config_manager_->getSystemConfig();

        if (delta > 0) {  // 向上滚动，放大
            scope_zoom_ = std::min(scope_zoom_.load() + system_config.zoom_step, system_config.max_zoom);
        } else {  // 向下滚动，缩小
            scope_zoom_ = std::max(scope_zoom_.load() - system_config.zoom_step, system_config.min_zoom);
        }

        current_config.scope_zoom = scope_zoom_;
        logInfo("🔍 " + current_config.scopes + "倍镜缩放调整为: " + std::to_string(scope_zoom_.load()));
    }
}

void WeaponRecognitionSystem::handleWeaponChange() {
    WeaponConfig& current_config = getCurrentConfig();

    if (current_config.scopes == "x6" || current_config.scopes == "x8") {
        scope_zoom_ = 1.6;  // 高倍镜重置为最大缩放值
        current_config.scope_zoom = 1.6;
    } else {
        scope_zoom_ = 1.0;  // 其他倍镜重置为1.0
        current_config.scope_zoom = 1.0;
    }
}

WeaponConfig& WeaponRecognitionSystem::getCurrentConfig() {
    return (current_weapon_type_ == WeaponType::RIFLE) ? rifle_config_ : sniper_config_;
}

const WeaponConfig& WeaponRecognitionSystem::getCurrentConfig() const {
    return (current_weapon_type_ == WeaponType::RIFLE) ? rifle_config_ : sniper_config_;
}

void WeaponRecognitionSystem::cleanup() {
    // 清理资源
    if (hotkey_manager_) {
        hotkey_manager_->unregisterAllHotkeys();
    }

    if (mouse_listener_) {
        mouse_listener_->stopListening();
    }

    logInfo("清理资源完成");
}

} // namespace WeaponRecognition
