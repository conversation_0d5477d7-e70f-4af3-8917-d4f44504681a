@echo off
echo ========================================
echo    PUBG武器识别系统 C++版 构建脚本
echo ========================================

:: 检查是否安装了CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到CMake，请先安装CMake
    pause
    exit /b 1
)

:: 检查是否安装了Visual Studio
where cl >nul 2>&1
if errorlevel 1 (
    echo 正在设置Visual Studio环境...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if errorlevel 1 (
            echo 错误: 未找到Visual Studio，请先安装Visual Studio
            pause
            exit /b 1
        )
    )
)

:: 创建构建目录
if not exist build mkdir build
cd build

:: 配置项目
echo 正在配置项目...
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Release
if errorlevel 1 (
    echo 配置失败，尝试使用Visual Studio 2019...
    cmake .. -G "Visual Studio 16 2019" -A x64 -DCMAKE_BUILD_TYPE=Release
    if errorlevel 1 (
        echo 错误: 项目配置失败
        pause
        exit /b 1
    )
)

:: 构建项目
echo 正在构建项目...
cmake --build . --config Release --parallel
if errorlevel 1 (
    echo 错误: 项目构建失败
    pause
    exit /b 1
)

:: 复制必要文件
echo 正在复制配置文件...
if not exist bin mkdir bin
copy ..\config\*.json bin\ >nul 2>&1
copy ..\nc bin\ /E /Y >nul 2>&1

echo.
echo ========================================
echo           构建完成！
echo ========================================
echo 可执行文件位置: build\bin\WeaponRecognition.exe
echo 配置文件位置: build\bin\config.json
echo.
echo 运行程序:
echo   cd build\bin
echo   WeaponRecognition.exe
echo.

:: 询问是否立即运行
set /p choice="是否立即运行程序? (y/n): "
if /i "%choice%"=="y" (
    cd bin
    WeaponRecognition.exe
)

pause
