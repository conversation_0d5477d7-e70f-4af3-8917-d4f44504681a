#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的Lua代码解密工具
修复解密过程中的问题，确保生成可运行的Lua代码
"""

import re

def decode_ascii_escape_sequences(text):
    """正确解码ASCII转义序列"""
    def replace_escape_sequence(match):
        # 获取完整的转义序列
        full_match = match.group(0)
        
        # 提取数字部分
        numbers = re.findall(r'\\(\d+)', full_match)
        
        result = ""
        for num_str in numbers:
            try:
                ascii_code = int(num_str)
                if 32 <= ascii_code <= 126:  # 可打印ASCII字符范围
                    result += chr(ascii_code)
            except ValueError:
                continue
        
        return result
    
    # 匹配所有形如 \数字 的转义序列
    pattern = r'\\(?:\d+)+'
    
    # 先处理引号内的转义序列
    def process_quoted_content(match):
        quote_char = match.group(1)  # " 或 '
        content = match.group(2)
        
        # 解码内容中的转义序列
        decoded_content = re.sub(pattern, replace_escape_sequence, content)
        
        return f'{quote_char}{decoded_content}{quote_char}'
    
    # 处理双引号和单引号内的内容
    text = re.sub(r'(["\'])((?:\\\\d+|[^"\'\\])*)\1', process_quoted_content, text)
    
    return text

def fix_lua_syntax(code):
    """修复Lua语法问题"""
    # 修复比较操作符
    code = re.sub(r'\s*=\s*=\s*', ' == ', code)
    code = re.sub(r'\s*<\s*=\s*', ' <= ', code)
    code = re.sub(r'\s*>\s*=\s*', ' >= ', code)
    code = re.sub(r'\s*~\s*=\s*', ' ~= ', code)
    
    # 修复其他操作符
    code = re.sub(r'\s*\+\s*', ' + ', code)
    code = re.sub(r'\s*-\s*', ' - ', code)
    code = re.sub(r'\s*\*\s*', ' * ', code)
    code = re.sub(r'\s*/\s*', ' / ', code)
    
    # 修复赋值操作符（但不影响比较操作符）
    code = re.sub(r'(?<![=!<>~])\s*=\s*(?!=)', ' = ', code)
    
    # 修复字符串连接
    code = re.sub(r'"\s*\+\s*"', '" + "', code)
    
    return code

def format_lua_properly(code):
    """正确格式化Lua代码"""
    # 基本的行分割
    code = code.replace(';', ';\n')
    
    # 处理函数定义
    code = re.sub(r'\bfunction\s*\(', '\nfunction(', code)
    code = re.sub(r'\bend\b', '\nend\n', code)
    
    # 处理控制结构
    code = re.sub(r'\bif\b', '\nif', code)
    code = re.sub(r'\bthen\b', ' then\n', code)
    code = re.sub(r'\belse\b', '\nelse\n', code)
    code = re.sub(r'\belseif\b', '\nelseif', code)
    
    code = re.sub(r'\bwhile\b', '\nwhile', code)
    code = re.sub(r'\bfor\b', '\nfor', code)
    code = re.sub(r'\bdo\b', ' do\n', code)
    
    code = re.sub(r'\breturn\b', '\nreturn', code)
    code = re.sub(r'\bbreak\b', '\nbreak', code)
    
    # 处理local声明
    code = re.sub(r'\blocal\b', '\nlocal', code)
    
    # 清理多余的空行和空格
    lines = []
    indent_level = 0
    
    for line in code.split('\n'):
        line = line.strip()
        if not line:
            continue
        
        # 简单的缩进逻辑
        if any(keyword in line for keyword in ['end', '}', 'else', 'elseif']):
            indent_level = max(0, indent_level - 1)
        
        # 添加缩进
        formatted_line = '    ' * indent_level + line
        lines.append(formatted_line)
        
        # 增加缩进级别
        if any(keyword in line for keyword in ['function', 'if', 'while', 'for', 'do', 'then', 'else']):
            if not any(end_keyword in line for end_keyword in ['end', 'break', 'return']):
                indent_level += 1
    
    return '\n'.join(lines)

def main():
    print("="*80)
    print("正确的Lua代码解密工具")
    print("="*80)
    
    # 读取原始文件
    try:
        with open('开镜50垂直1（垂直可随意修改）.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            encrypted_line = lines[-1].strip()
    except FileNotFoundError:
        print("错误：找不到源文件")
        return
    
    print(f"原始加密代码长度: {len(encrypted_line)} 字符")
    
    # 步骤1：解码ASCII转义序列
    print("\n步骤1: 正确解码ASCII转义序列...")
    decoded = decode_ascii_escape_sequences(encrypted_line)
    
    # 步骤2：修复Lua语法
    print("步骤2: 修复Lua语法...")
    syntax_fixed = fix_lua_syntax(decoded)
    
    # 步骤3：格式化代码
    print("步骤3: 格式化代码...")
    formatted = format_lua_properly(syntax_fixed)
    
    # 保存修复后的代码
    output_file = 'corrected_lua_code.lua'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(formatted)
    
    print(f"\n修复后的代码已保存到 '{output_file}'")
    
    # 显示前50行预览
    print("\n" + "="*80)
    print("修复后代码预览 (前50行):")
    print("="*80)
    
    preview_lines = formatted.split('\n')[:50]
    for i, line in enumerate(preview_lines, 1):
        print(f"{i:3d}: {line}")
    
    if len(formatted.split('\n')) > 50:
        print(f"... (还有 {len(formatted.split('\n')) - 50} 行)")
    
    print(f"\n总行数: {len(formatted.split('\n'))}")
    print(f"文件大小: {len(formatted)} 字符")
    
    print("\n" + "="*80)
    print("修复说明:")
    print("="*80)
    print("1. 正确解码了所有ASCII转义序列")
    print("2. 修复了操作符间距问题")
    print("3. 修复了Lua语法结构")
    print("4. 添加了适当的代码缩进")
    print("5. 确保代码符合Lua语法规范")
    
    print(f"\n请使用 '{output_file}' 文件在罗技驱动中测试")

if __name__ == "__main__":
    main()
