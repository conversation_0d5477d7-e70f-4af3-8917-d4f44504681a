EnablePrimaryMouseButtonEvents(true)
pick = 4 --（鬼手按键默认鼠标上侧键，3是中键，4是下侧键，5是上侧键，如果不想用这几个按键改成大于5的）

global_vertical_sensitivity_multiplier = 121/100 --垂直灵敏度比如100的意思就是垂直灵敏度1

first_shot_offset = 0.2 -- 第一发子弹向下偏移量，例如 0.2


global_breath_multiplier = 1  --屏息灵敏度(要自己调）

global_scope_multipliers = { --倍镜灵敏度，分子+-1范围内调整
    None = 1,
    reddot = 1,
    quanxi = 1,
    x2 = 99.1/64.52,
    x3 = 104/72.41,  
    x4 = 103/67.21,
    x6 = 109.5/75.86,
}    

x_accumulator = 5 
 
 function handle_x_movement(now_cursor_x)
     move_x = 0
    if now_cursor_x > 0 then
        x_accumulator = x_accumulator - 0.5 
    elseif now_cursor_x < 0 then
        x_accumulator = x_accumulator + 0.5  
    end
    
    if math.abs(x_accumulator) >= 1 then
        move_x = math.floor(x_accumulator)
        x_accumulator = x_accumulator - move_x
    end
    return move_x
end

global_sensitivity_multiplier = 30/75.2


 base_coefficients = { --各枪械总系数，比如只有某把枪所有配件都下压过度，则调这个
    Berry = 1,
    AUG = 1.0,
    AKM = 1.0,
    M416 = 1.0,
    ACE32 = 1.0,
    G36C = 1.0,
    SCAR = 1.0,
    QBZ = 1.0,
    K2 = 1.0,
    M16 = 1.0,
    MK47 = 1.1,
    GROZA = 1.0,
    FAMAS = 1.0,
    PP19 = 1.0,
    TOM = 1.0,
    UMP = 1.0,
    UZI = 1.0,
    VECTOR = 1.0,
    MP5 = 1.0,
    P90 = 1.0,
    JS9 = 1.0,
    MP9 = 1.0,
    SLR = 1.5,
    MINI = 1.7,
    SKS = 1.05,
    MK12 = 1.7,
    QBU = 1.55,
    DLG = 1.0,
    VSS = 1.0,
    MK14 = 1.8,
    M249 = 1.0,
    MG3 = 1.0,
}          
            

                        

 attachment_multipliers = {
 None = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
            
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.78,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455, 
            
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },
    Berry = { 
        poses = {
            None = 1.02,
            stand = 1.02,
            down = 0.83,
            crawl = 0.58,
             
        },
        muzzles = {
            None = 1,
            xy1 = 0.85,
            bc1 = 0.76,
            xx = 1,
            zt = 0.87,
        },
         grips = {
            None = 1,
            angle = 1,
            red = {  
                segments = { 
                    { count = 8, multiplier = 0.9 },
                    { count = 40, multiplier = 0.8 },
                }
            },
            line = 0.78,
             thumb = {  
                segments = { 
                    { count = 17, multiplier = 0.8 },
                    { count = 40, multiplier = 0.75 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.9 },
                    
                    { count = 40, multiplier = 0.85 },
                }
            },
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.444,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   AUG = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.8,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.84,
            bc1 = 0.78,
            xx = 1,
            zt = 0.865,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = {  
                segments = {
                    { count = 10, multiplier = 0.9 },
                    { count = 30, multiplier = 0.78 },    
                    { count = 40, multiplier = 0.85 },
                    
                }
            },
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 3, multiplier = 0.8 },
                    { count = 10, multiplier = 0.82 },
                    { count = 40, multiplier = 0.78 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.9 },
                    
                    { count = 40, multiplier = 0.84 },
                }
            },
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.444,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   AKM = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.47,
        },
        muzzles = {
            None = 1,
            xy1 = 0.85,
            bc1 = 0.76,
            xx = 1,
            zt = 0.86,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.55,
            reddot = 0.55,
            quanxi = 0.55,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.444,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   M416 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.8,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.79,
            xx = 1,
            zt = 0.89,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = {  
                segments = {
                    { count = 8, multiplier = 0.9 },
                     { count = 27, multiplier = 0.8 },
                      { count = 40, multiplier = 0.9 },

                    
                    
                }
            },
            line = 0.8,
            thumb = {  
                segments = { 
                    { count = 3, multiplier = 0.8 },
                    { count = 10, multiplier = 0.82 },
                    { count = 30, multiplier = 0.77 },
                    { count = 40, multiplier = 0.8 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.9 },
                    
                    { count = 40, multiplier = 0.84 },
                }
            },
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
            normal = {  
                segments = {  
                    { count = 2, multiplier = 0.87 },
                    { count = 20, multiplier = 0.94 },
                    { count = 40, multiplier = 1.02 },
                }
            },
            heavy = {  
                segments = {  
                    { count = 11, multiplier = 0.9 },
                     { count = 25, multiplier = 0.78 },
                      { count = 37, multiplier = 0.88 },
                      { count = 40, multiplier = 0.92 },
                }
            },
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   ACE32 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
             red = {  
                segments = {
                    { count = 6, multiplier = 1 },
                    { count = 25, multiplier = 0.8 },
                    { count = 40, multiplier = 0.73 },
                   
                    

                   
                }
            },
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 3, multiplier = 0.8 },
                    { count = 10, multiplier = 0.82 },
                    { count = 30, multiplier = 0.74 },
                    { count = 40, multiplier = 0.72 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.92 },
                    
                    { count = 40, multiplier = 0.8 },
                }
            },
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
            normal = {  
                segments = {  
                    { count = 7, multiplier = 1.02 },
                    { count = 27, multiplier = 0.97 },
                     { count = 40, multiplier = 0.92 },
                }
            },
            heavy = {  
                segments = {  
                    { count = 11, multiplier = 0.9 },
                     { count = 25, multiplier = 0.78 },
                      { count = 37, multiplier = 0.88 },
                      { count = 40, multiplier = 0.92 },
                }
            },
        
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   G36C = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.79,
            line = 0.78,
            thumb = 0.76,
            light = 0.79,
        },
        scopes = {
            None = 0.55,
            reddot = 0.55,
            quanxi = 0.55,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   SCAR = { 
        poses = {
            None = 0.96,
            stand = 0.96,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.84,
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 17, multiplier = 0.82 },
                    { count = 40, multiplier = 0.85 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.9 },
                    
                    { count = 40, multiplier = 0.85 },
                }
            },
        },
        scopes = {
            None = 0.5,
            reddot = 0.5,
            quanxi = 0.5,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   QBZ = { 
        poses = {
            None = 0.92,
            stand = 0.92,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.88,
            bc1 = 0.8,
            xx = 1,
            zt = 0.88,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = {  
                segments = {
                    { count = 6, multiplier = 0.9 },
                    { count = 12, multiplier = 0.8 },
                    { count = 40, multiplier = 0.75 },
                    

                   
                }
            },
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 12, multiplier = 0.82 },
                    { count = 40, multiplier = 0.84 },
                }
            },
            light = 0.79,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   K2 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.55,
            reddot = 0.55,
            quanxi = 0.55,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   M16 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.83,
            bc1 = 0.74,
            xx = 1,
            zt = 0.84,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1.8,
            x3 = 2.6,
            x4 = 3.6,
            x6 = 2.6,

        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = {  
                segments = {  
                      { count = 15, multiplier = 0.92 },
                      { count = 40, multiplier = 0.81 },
                }
            },
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   MK47 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.53,
        },
        muzzles = {
            None = 1,
            xy1 = 0.84,
            bc1 = 0.75,
            xx = 1,
            zt = 0.84,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.86,
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 12, multiplier = 0.8 },
                    { count = 40, multiplier = 0.85 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.92 },
                    
                    { count = 40, multiplier = 0.99 },
                }
            },
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1.8,
            x3 = 2.6,
            x4 = 3.6,
            x6 = 2.6,

        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   GROZA = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.7,
            crawl = 0.47,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   FAMAS = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.8,
            xx = 1,
            zt = 0.83,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   PP19 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.77,
            crawl = 0.7,
        },
        muzzles = {
            None = 1,
            bc3 = 0.8,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
            line = 0.78,
        },
        scopes = {
            None = 0.51,
            reddot = 0.51,
            quanxi = 0.51,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   TOM = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.7,
            crawl = 0.62,
        },
        muzzles = {
            None = 1,
            xx1 = 1,
        },
        grips = {
            None = 1,
            line = 0.78	,
        },
        scopes = {
            None = 0.51,
            reddot = 0.51,
            quanxi = 0.51,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   UMP = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.74,
            crawl = 0.7,
        },
        muzzles = {
            None = 1,
            bc3 = 0.8,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.935,
            line = 0.79,
            thumb = 0.88,
            light = 0.94,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   UZI = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.73,
            crawl = 0.6,
        },
        muzzles = {
            None = 1,
            bc3 = 0.68,
            xy3 = 0.88,
            xx1 = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.51,
            reddot = 0.51,
            quanxi = 0.51,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   VECTOR = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.64,
        },
        muzzles = {
            None = 1,
            bc3 = 0.8,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
            red = 0.9,
            line = 0.79,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
            normal = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   MP5 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.67,
            crawl = 0.53,
        },
        muzzles = {
            None = 1,
            bc3 = 0.6,
            xy3 = 0.82,
            xx1 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.75,
            thumb = 0.83,
            light = 0.9,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
            normal = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   P90 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.77,
            crawl = 0.67,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   JS9 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.73,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            bc3 = 0.58,
            xy3 = 0.82,
            xx1 = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   MP9 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.73,
            crawl = 0.63,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },
 SLR = { 
        poses = {
            None = 1,
            stand = 1.02,
            down = 0.77,
            crawl = 0.45,            
            standBurst = 1.25, 
            downBurst = 0.82    
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.88,
                   
        },
        scopes = {
            None = 0.46,
            reddot = 0.46,
            quanxi = 0.46,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 0.9,
            x8 = 1,
            x4burst = 1.2,
            x8burst = 0.7,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    MINI = { 
        poses = {
            None = 1.05,
            stand = 1.05,
            down = 0.72,
            crawl = 0.45,
            standBurst = 1.46, 
            downBurst = 1    
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.89,
                   
        },
        scopes = {
            None = 0.4,
            reddot =0.4,
            quanxi = 0.4,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 1,
            x8 = 1.1,
            x4burst = 1.4,
            x8burst = 0.8,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    SKS = { 
        poses = {
            None = 1,
            stand = 1.099,
            down = 2,
            crawl = 1.7,
            standBurst = 1.5, 
            downBurst = 1
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.87,
                   
        },
        scopes = {
            None = 0.465,
            reddot = 0.465,
            quanxi = 0.465,
            x2 = 1,
            x3 = 1,
            x4 = 0.6,
            x6 = 1,
            x8 = 0.6,
            x4burst = 1.37,
            x8burst = 0.8,

        },
         grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    MK12 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.45,
            standBurst = 1.43, 
            downBurst = 1 
        },
        
         muzzles = {
           None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 0.38,
            reddot = 0.38,
            quanxi = 0.38,
            x2 = 1,
            x3 = 1,
            x4 = 1.08,
            x6 = 1,
            x8 = 1.08,
            x4burst = 1.4,
            x8burst = 0.8,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    QBU = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.45, 
            standBurst = 1.43, 
            downBurst = 1  
        },
         muzzles = {
           None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 0.38,
            reddot = 0.38,
            quanxi = 0.38,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
            x4burst = 1.4,
            x8burst = 0.8,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    DLG = { 
        poses = {
            None = 1.3,
            stand = 1.3,
            down = 0.84, 
            crawl = 0.45,
            
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1.8,
            x4 = 1.5,
            x6 = 1.8,
            x8 = 1.5,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    VSS = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,  
        },
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 1,
                   
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
  
    MK14 = { 
        poses = {
            None = 1,
            stand = 0.95,
            down = 0.77,
            crawl = 0.45, 
            standBurst = 1, 
            downBurst = 1
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 1,
            x8 = 1.2,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    M249 = { 
        poses = {
            None = 1.28,
            stand = 1.28,
            down = 0.73,
            crawl = 0.3,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.55,  
            reddot = 0.55, 
            quanxi = 0.55, 
	     x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
			 normal = {  
                segments = {  
                    { count = 90, multiplier = 1},
                    { count = 150, multiplier = 1.1 },
                }
            },
			heavy = {  
                segments = {  
                    { count = 90, multiplier = 0.83},
                     { count = 150, multiplier = 0.72 },

                }
            },
			
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

	MG3 = { 
        poses = {
            None = 3,
            stand = 3,
            down = 1.78,
            crawl = 0.65,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,  
            reddot = 0.52, 
            quanxi = 0.52, 
			x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },
}
    

 recoil_patterns = {
Berry = {
        default = {
{1, 18},
{2, 29},
{3, 17},
{4, 39},
{5, 21},
{6, 41},
{7, 21},
{8, 45},
{9, 25},
{10, 49},
{11, 25},
{12, 49},
{13, 25},
{14, 49},
{15, 29},
{16, 57},
{17, 30},
{18, 59},
{19, 30},
{20, 59},
{21, 30},
{22, 59},
{23, 30},
{24, 59},
{25, 30},
{26, 59},
{27, 30},
{28, 59},
{29, 30},
{30, 59},
{31, 30},
{32, 59},
{33, 30},
{34, 59},
{35, 30},
{36, 59},
{37, 30},
{38, 59},
{39, 30},
{40, 59},
{41, 30}
        },
    },
None = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 45},
{31, 23},
{32, 45},
{33, 23},
{34, 45},
{35, 23},
{36, 45},
{37, 23},
{38, 45},
{39, 23},
{40, 45},
{41, 23}
        },
    },
    

    AUG = {
        default = {
{1, 24},
{2, 17},
{3, 9},
{4, 25},
{5, 16},
{6, 35},
{7, 20},
{8, 39},
{9, 20},
{10, 41},
{11, 23},
{12, 49},
{13, 25},
{14, 49},
{15, 26},
{16, 51},
{17, 27},
{18, 53},
{19, 27},
{20, 53},
{21, 27},
{22, 53},
{23, 27},
{24, 53},
{25, 27},
{26, 53},
{27, 27},
{28, 53},
{29, 27},
{30, 53},
{31, 27},
{32, 55},
{33, 28},
{34, 55},
{35, 28},
{36, 55},
{37, 28},
{38, 55},
{39, 28},
{40, 55},
{41, 28}
        },
    },

    AKM = {
        default = {
{1, 16},
{2, 25},
{3, 13},
{4, 23},
{5, 16},
{6, 31},
{7, 16},
{8, 35},
{9, 20},
{10, 39},
{11, 21},
{12, 41},
{13, 21},
{14, 41},
{15, 21},
{16, 43},
{17, 22},
{18, 43},
{19, 22},
{20, 43},
{21, 22},
{22, 43},
{23, 22},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 43},
{31, 22},
{32, 43},
{33, 22},
{34, 43},
{35, 22},
{36, 43},
{37, 22},
{38, 43},
{39, 22},
{40, 43},
{41, 22}
        },
    },  

    M416 = {
        default = {
{1, 23},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 39},
{25, 19},
{26, 39},
{27, 19},
{28, 39},
{29, 19},
{30, 37},
{31, 18},
{32, 37},
{33, 18},
{34, 37},
{35, 18},
{36, 37},
{37, 18},
{38, 37},
{39, 18},
{40, 37},
{41, 18}
        },
    },

    ACE32 = {
        default = {
{1, 18},
{2, 23},
{3, 12},
{4, 29},
{5, 17},
{6, 37},
{7, 20},
{8, 39},
{9, 21},
{10, 41},
{11, 21},
{12, 43},
{13, 23},
{14, 45},
{15, 23},
{16, 45},
{17, 23},
{18, 47},
{19, 25},
{20, 51},
{21, 26},
{22, 51},
{23, 26},
{24, 51},
{25, 27},
{26, 53},
{27, 27},
{28, 53},
{29, 27},
{30, 53},
{31, 27},
{32, 53},
{33, 27},
{34, 53},
{35, 27},
{36, 53},
{37, 27},
{38, 53},
{39, 27},
{40, 53},
{41, 27}
        },
    },

    G36C = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 45},
{31, 23},
{32, 45},
{33, 23},
{34, 45},
{35, 23},
{36, 45},
{37, 23},
{38, 45},
{39, 23},
{40, 45},
        },
    },

    SCAR = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 41},
{25, 21},
{26, 41},
{27, 21},
{28, 41},
{29, 21},
{30, 41},
{31, 21},
{32, 41},
{33, 21},
{34, 41},
{35, 21},
{36, 41},
{37, 21},
{38, 41},
{39, 21},
{40, 41},
{41, 21}
        },
    },

    QBZ = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 45},
{31, 23},
{32, 45},
{33, 23},
{34, 45},
{35, 23},
{36, 45},
{37, 23},
{38, 45},
{39, 23},
{40, 45},
{41, 23}
        },
    },

    K2 = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 41},
{25, 21},
{26, 41},
{27, 21},
{28, 41},
{29, 21},
{30, 41},
{31, 21},
{32, 41},
{33, 21},
{34, 41},
{35, 21},
{36, 41},
{37, 21},
{38, 41},
{39, 21},
{40, 41},
{41, 21}
        },
    },

    M16 = {
        default = {
{1, 6},
{2, 11},
{3, 7},
{4, 13},
{5, 8},
{6, 23},
{7, 12},
{8, 23},
{9, 12},
{10, 23},
{11, 12},
{12, 23},
{13, 12},
{14, 23},
{15, 12},
{16, 23},
{17, 12},
{18, 23},
{19, 12},
{20, 23},
{21, 12},
{22, 23},
{23, 12},
{24, 23},
{25, 12},
{26, 23},
{27, 12},
{28, 23},
{29, 23}, 
{30, 21}, 
{31, 11},
{32, 21}, 
{33, 11},
{34, 21}, 
{35, 23}, 
{36, 23},
{37, 12},
{38, 23},
{39, 12},
{40, 23}, 
{41, 12},
{42, 23}
       },
    },

    MK47 = {
        default = {
{1, 6},
{2, 11},
{3, 9},
{4, 17},
{5, 10},
{6, 27},
{7, 14},
{8, 27},
{9, 14},
{10, 27},
{11, 14},
{12, 27},
{13, 14},
{14, 27},
{15, 14},
{16, 27},
{17, 14},
{18, 27},
{19, 14},
{20, 27},
{21, 14},
{22, 27},
{23, 14},
{24, 27},
{25, 14},
{26, 27},
{27, 14},
{28, 27},
{29, 14},
{30, 27},
{31, 14},
{32, 27}
       },
    },

    GROZA = {
        default = {
{1, 16},
{2, 15},
{3, 13},
{4, 27},
{5, 14},
{6, 27},
{7, 14},
{8, 27},
{9, 14},
{10, 29},
{11, 15},
{12, 31},
{13, 16},
{14, 33},
{15, 18},
{16, 39},
{17, 20},
{18, 39},
{19, 20},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 41},
{25, 21},
{26, 41},
{27, 21},
{28, 41},
{29, 21},
{30, 42},
{31, 21.5},
{32, 42},
{33, 21.5},
{34, 42},
{35, 21.5},
{36, 42},
{37, 21.5},
{38, 42},
{39, 21.5},
{40, 42},
{41, 21.5}
        },
    },

    FAMAS = {
        default = {
{1, 18},
{2, 11},
{3, 9},
{4, 17},
{5, 10},
{6, 27},
{7, 17},
{8, 35},
{9, 19},
{10, 39},
{11, 21},
{12, 41},
{13, 21},
{14, 41},
{15, 22},
{16, 43},
{17, 22},
{18, 43},
{19, 22},
{20, 43},
{21, 23},
{22, 45},
{23, 23},
{24, 45},
{25, 23},
{26, 45},
{27, 23},
{28, 45},
{29, 23},
{30, 45},
{31, 23},
{32, 45},
{33, 23},
{34, 45},
{35, 23},
{36, 45},
{37, 23},
{38, 45},
{39, 23},
{40, 45},
{41, 23}
      },
    },

    PP19 = {
        default = {
{1, 7},
{2, 13},
{3, 8},
{4, 17},
{5, 10},
{6, 21},
{7, 12},
{8, 23},
{9, 12},
{10, 23},
{11, 12},
{12, 20},
{13, 10.5},
{14, 20},
{15, 10.5},
{16, 20},
{17, 10.5},
{18, 20},
{19, 10.5},
{20, 20},
{21, 10.5},
{22, 20},
{23, 10.5},
{24, 20},
{25, 10.5},
{26, 20},
{27, 10.5},
{28, 20},
{29, 10.5},
{30, 20},
{31, 10.5},
{32, 20},
{33, 10.5},
{34, 20},
{35, 10.5},
{36, 20},
{37, 10.5},
{38, 20},
{39, 10.5},
{40, 20},
{41, 10.5},
{42, 20},
{43, 10.5},
{44, 20},
{45, 10.5},
{46, 20},
{47, 10.5},
{48, 20},
{49, 10.5},
{50, 20},
{51, 10.5},
{52, 20},
{53, 10.5}
      },
    },

    TOM = {
        default = {
{1, 8},
{2, 15},
{3, 9},
{4, 21},
{5, 12},
{6, 23},
{7, 13},
{8, 25},
{9, 14},
{10, 29},
{11, 16},
{12, 43},
{13, 22},
{14, 43},
{15, 22},
{16, 43},
{17, 22},
{18, 43},
{19, 22},
{20, 43},
{21, 22},
{22, 43},
{23, 22},
{24, 43},
{25, 22},
{26, 43},
{27, 20},
{28, 39},
{29, 20},
{30, 39},
{31, 20},
{32, 39},
{33, 20},
{34, 39},
{35, 20},
{36, 39},
{37, 20},
{38, 39},
{39, 20},
{40, 39},
{41, 20},
{42, 39},
{43, 20},
{44, 39},
{45, 20},
{46, 39},
{47, 20},
{48, 39},
{49, 20},
{50, 39}
      },
    },

    UMP = {
        default = {
{1, 9},
{2, 17},
{3, 10},
{4, 21},
{5, 12},
{6, 25},
{7, 13},
{8, 25},
{9, 14},
{10, 27},
{11, 14},
{12, 28},
{13, 14.5},
{14, 28},
{15, 14.5},
{16, 28},
{17, 14.5},
{18, 28},
{19, 14.5},
{20, 28},
{21, 14.5},
{22, 28},
{23, 14.5},
{24, 28},
{25, 14.5},
{26, 28},
{27, 14.5},
{28, 28},
{29, 14.5},
{30, 28},
{31, 14.5},
{32, 28},
{33, 14.5},
{34, 28},
{35, 14.5}
      },
    },

    UZI = {
        default = {
{1, 7},
{2, 13},
{3, 8},
{4, 15},
{5, 9},
{6, 19},
{7, 11},
{8, 23},
{9, 13},
{10, 27},
{11, 14},
{12, 40},
{13, 20.5},
{14, 40},
{15, 20.5},
{16, 40},
{17, 20.5},
{18, 40},
{19, 20.5},
{20, 44},
{21, 22.5},
{22, 44},
{23, 22.5},
{24, 44},
{25, 22.5},
{26, 44},
{27, 22.5},
{28, 46},
{29, 23.5},
{30, 46},
{31, 23.5},
{32, 46},
{33, 23.5},
{34, 46},
{35, 23.5}
      },
    },

    VECTOR = {
        default = {
{ 1, 11 },
{ 2, 19 },
{ 3, 11 },
{ 4, 23 },
{ 5, 13 },
{ 6, 27 },
{ 7, 15 },
{ 8, 31 },
{ 9, 17 },
{10, 33 },
{11, 19 },
{12, 40 },
{13, 22.5 },
{14, 48 },
{15, 27.5 },
{16, 54 },
{17, 27.5 },
{18, 54 },
{19, 27.5 },
{20, 54 },
{21, 27.5 },
{22, 54 },
{23, 27.5 },
{24, 54 },
{25, 27.5 },
{26, 54 },
{27, 27.5 },
{28, 54 },
{29, 27.5 },
{30, 54 },
{31, 27.5 },
{32, 54 },
{33, 27.5 }
      },
    },

    MP5 = {
        default = {
{ 1, 16 },
{ 2, 17 },
{ 3, 9 },
{ 4, 23 },
{ 5, 14 },
{ 6, 29 },
{ 7, 16 },
{ 8, 33 },
{ 9, 18 },
{10, 37 },
{11, 19 },
{12, 37 },
{13, 19 },
{14, 38 },
{15, 19.5 },
{16, 38 },
{17, 19.5 },
{18, 38 },
{19, 19.5 },
{20, 38 },
{21, 19.5 },
{22, 38 },
{23, 19.5 },
{24, 38 },
{25, 19.5 },
{26, 38 },
{27, 19.5 },
{28, 38 },
{29, 19.5 },
{30, 38 },
{31, 20 },
{32, 39 },
{33, 20 },
{34, 39 },
{35, 20 },
{36, 39 },
{37, 20 },
{38, 39 },
{39, 20 },
{40, 39 }
      },
    },

    P90 = {
        default = {
{ 1, 8 },
{ 2, 15 },
{ 3, 9 },
{ 4, 21 },
{ 5, 12 },
{ 6, 23 },
{ 7, 13 },
{ 8, 25 },
{ 9, 13 },
{10, 23 },
{11, 14 },
{12, 27 },
{13, 15 },
{14, 29 },
{15, 12 },
{16, 17.2 },
{17, 9.1 },
{18, 17.2 },
{19, 9.1 },
{20, 17.2 },
{21, 9.1 },
{22, 17.2 },
{23, 9.1 },
{24, 17.2 },
{25, 9.1 },
{26, 17.2 },
{27, 9.1 },
{28, 17.2 },
{29, 9.1 },
{30, 17.2 },
{31, 9.1 },
{32, 17.2 },
{33, 9.1 },
{34, 17.2 },
{35, 9.1 },
{36, 17.2 },
{37, 9.1 },
{38, 17.2 },
{39, 9.1 },
{40, 17.2 },
{41, 9.1 },
{42, 17.2 },
{43, 9.1 },
{44, 17.2 },
{45, 9.1 },
{46, 17.2 },
{47, 9.1 },
{48, 17.2 },
{49, 9.1 },
{50, 17.2 }
      },
    },

    JS9 = {
        default = {
{ 1, 10 },
{ 2, 13 },
{ 3, 7 },
{ 4, 19 },
{ 5, 12 },
{ 6, 25 },
{ 7, 13 },
{ 8, 25 },
{ 9, 13 },
{10, 25 },
{11, 13 },
{12, 25 },
{13, 15 },
{14, 36 },
{15, 18.5 },
{16, 36 },
{17, 18.5 },
{18, 36 },
{19, 18.5 },
{20, 36 },
{21, 18.5 },
{22, 36 },
{23, 18.5 },
{24, 36 },
{25, 18.5 },
{26, 36 },
{27, 18.5 },
{28, 36 },
{29, 18.5 },
{30, 36 },
{31, 18.5 },
{32, 36 },
{33, 18.5 },
{34, 36 },
{35, 18.5 },
{36, 36 },
{37, 18.5 },
{38, 36 },
{39, 18.5 },
{40, 36 }
      },
    },

    MP9 = {
        default = {
{1, 7 },
{2, 13 },
{3, 8 },
{4, 15 },
{5, 9 },
{6, 19 },
{7, 11 },
{8, 23 },
{9, 12 },
{10, 23 },
{11, 12 },
{12, 24 },
{13, 12.5},
{14, 24 },
{15, 12.5},
{16, 24 },
{17, 12.5},
{18, 22 },
{19, 8.5 },
{20, 16 },
{21, 7.5 },
{22, 14 },
{23, 7.5 },
{24, 14 },
{25, 7.5 },
{26, 14 },
{27, 7.5 },
{28, 14 },
{29, 7.5 },
{30, 14 },
{31, 7.5 },
{32, 14 },
{33, 7.5 },
{34, 14 },
{35, 7.5 }
      },
    },
   
M249 = {
        default = {
{ 1, 15 },
{ 2, 9 },
{ 3, 4 },
{ 4, 15 },
{ 5, 9 },
{ 6, 19 },
{ 7, 10 },
{ 8, 23 },
{ 9, 12 },
{10, 19 },
{11, 10 },
{12, 19 },
{13, 8 },
{14, 13 },
{15, 8 },
{16, 15 },
{17, 7 },
{18, 13 },
{19, 7 },
{20, 13 },
{21, 7 },
{22, 13 },
{23, 7 },
{24, 13 },
{25, 7 },
{26, 13 },
{27, 7 },
{28, 13 },
{29, 7 },
{30, 13 },
{31, 7 },
{32, 13 },
{33, 7 },
{34, 13 },
{35, 7 },
{36, 13 },
{37, 7 },
{38, 13 },
{39, 7 },
{40, 13 },
{41, 7 },
{42, 13 },
{43, 7 },
{44, 13 },
{45, 7 },
{46, 13 },
{47, 7 },
{48, 13 },
{49, 7 },
{50, 13 },
{51, 7 },
{52, 13 },
{53, 7 },
{54, 13 },
{55, 7 },
{56, 13 },
{57, 7 },
{58, 13 },
{59, 7 },
{60, 13 },
{61, 7 },
{62, 13 },
{63, 7 },
{64, 13 },
{65, 7 },
{66, 13 },
{67, 7 },
{68, 13 },
{69, 7 },
{70, 13 },
{71, 7 },
{72, 13 },
{73, 7 },
{74, 13 },
{75, 7 },
{76, 13 },
{77, 7 },
{78, 13 },
{79, 7 },
{80, 13 },
{81, 7 },
{82, 13 },
{83, 7 },
{84, 13 },
{85, 7 },
{86, 13 },
{87, 7 },
{88, 13 },
{89, 7 },
{90, 13 },
{91, 7 },
{92, 13 },
{93, 7 },
{94, 13 },
{95, 7 },
{96, 13 },
{97, 7 },
{98, 13 },
{99, 7 },
{100, 13},
{101, 7 },
{102, 13},
{103, 7 },
{104, 13},
{105, 7 },
{106, 13},
{107, 7 },
{108, 13},
{109, 7 },
{110, 13},
{111, 7 },
{112, 13},
{113, 7 },
{114, 13},
{115, 7 },
{116, 13},
{117, 7 },
{118, 13},
{119, 7 },
{120, 13},
{121, 7 },
{122, 13},
{123, 7 },
{124, 13},
{125, 7 },
{126, 13},
{127, 7 },
{128, 13},
{129, 7 },
{130, 13},
{131, 7 },
{132, 13},
{133, 7 },
{134, 13},
{135, 7 },
{136, 13},
{137, 7 },
{138, 13},
{139, 7 },
{140, 13},
{141, 7 },
{142, 13},
{143, 7 },
{144, 13},
{145, 7 },
{146, 13},
{147, 7 },
{148, 13},
{149, 7 },
{150, 13},
{151, 7 },
{152, 13},
{153, 7 },
{154, 13},
{155, 7 },
{156, 13},
{157, 7 },
{158, 13},
{159, 7 },
{160, 13},
{161, 7 },
{162, 13},
{163, 7 },
{164, 13},
{165, 7 },
{166, 13},
{167, 7 },
{168, 13},
{169, 7 },
{170, 13}

        },
    },  
 MG3 = {
        default = {
{ 1, 11 },
{ 2, 1 },
{ 3, 1 },
{ 4, 5 },
{ 5, 3 },
{ 6, 5 },
{ 7, 3 },
{ 8, 5 },
{ 9, 3 },
{10, 5 },
{11, 2 },
{12, 3 },
{13, 2 },
{14, 3 },
{15, 1 },
{16, 3 },
{17, 2 },
{18, 3 },
{19, 2 },
{20, 3 },
{21, 2 },
{22, 3 },
{23, 2 },
{24, 3 },
{25, 2 },
{26, 1 },
{27, 1 },
{28, 1 },
{29, 1 },
{30, 1 },
{31, 1 },
{32, 1 },
{33, 1 },
{34, 1 },
{35, 3 },
{36, 3 },
{37, 2 },
{38, 3 },
{39, 2 },
{40, 3 },
{41, 2 },
{42, 3 },
{43, 2 },
{44, 3 },
{45, 2 },
{46, 3 },
{47, 2 },
{48, 3 },
{49, 2 },
{50, 3 },
{51, 2 },
{52, 3 },
{53, 2 },
{54, 3 },
{55, 2 },
{56, 3 },
{57, 2 },
{58, 3 },
{59, 2 },
{60, 3 },
{61, 2 },
{62, 3 },
{63, 2 },
{64, 3 },
{65, 2 },
{66, 3 },
{67, 2 },
{68, 3 },
{69, 2 },
{70, 3 },
{71, 2 },
{72, 3 },
{73, 2 },
{74, 3 },
{75, 2 }
        },
    },  
   
    
    MINI = {
        default = {
{1, 6},
{2, 11},
{3, 6},
{4, 11},
{5, 6},
{6, 11},
{7, 6},
{8, 11},
{9, 6},
{10, 11},
{11, 6},
{12, 11},
{13, 6},
{14, 11},
{15, 6},
{16, 11},
{17, 6},
{18, 11},
{19, 6},
{20, 11},
{21, 6},
{22, 11},
{23, 6},
{24, 11},
{25, 6},
{26, 11},
{27, 6},
{28, 11},
{29, 6},
{30, 11}

        },
        burst = {

{1, 10},
{2, 12},
{3, 12},
{4, 32},
{5, 15},
{6, 31},
{7, 15},
{8, 31},
{9, 15},
{10, 31},
{11, 15},
{12, 31},
{13, 15},
{14, 31},
{15, 16},
{16, 31},
{17, 16},
{18, 31},
{19, 16},
{20, 31},
{21, 16},
{22, 31},
{23, 16},
{24, 31},
{25, 16},
{26, 31},
{27, 16},
{28, 31},
{29, 16},
{30, 31},
{31, 16},
{32, 31},
        },
    },
SLR = {
        default = {
{1, 5},
{2, 9},
{3, 5},
{4, 9},
{5, 5},
{6, 9},
{7, 5},
{8, 9},
{9, 5},
{10, 9},
{11, 5},
{12, 9},
{13, 5},
{14, 9},
{15, 5},
{16, 9},
{17, 5},
{18, 9},
{19, 5},
{20, 9}
        },
        burst = {

{1, 18},
{2, 18},
{3, 21},
{4, 26},
{5, 22},
{6, 32},
{7, 25},
{8, 32},
{9, 25},
{10, 32},
{11, 24},
{12, 30},
{13, 24},
{14, 30},
{15, 24},
{16, 30},
{17, 20},
{18, 30},
{19, 20},
{20, 30},
{21, 20},
{22, 0},
{23, 0},
{24, 0},
{25, 0},
{26, 0},
{27, 0},
{28, 0},
{29, 0},
{30, 0},
{31, 0},
{32, 0},
        },
    }, 
    SKS = {
        default = {
{1, 4},
{2, 7},
{3, 4},
{4, 7},
{5, 4},
{6, 7},
{7, 4},
{8, 7},
{9, 4},
{10, 7},
{11, 4},
{12, 7},
{13, 4},
{14, 7},
{15, 4},
{16, 7},
{17, 4},
{18, 7},
{19, 4},
{20, 7}
        },
        
    burst = {
{1, 14},
{2, 21},
{3, 17},
{4, 33},
{5, 22},
{6, 49},
{7, 25},
{8, 49},
{9, 24},
{10, 47},
{11, 28},
{12, 55},
{13, 28},
{14, 55},
{15, 28},
{16, 55},
{17, 28},
{18, 55},
{19, 26},
{20, 53},
{21, 26},
{22, 0},
{23, 0},
{24, 0},
{25, 0},
{26, 0},
{27, 0},
{28, 0},
{29, 0},
{30, 0},
{31, 0},
{32, 0},
        },

        DLG = {

{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    QBU = {
        default = {
{1, 6},
{2, 11},
{3, 6},
{4, 11},
{5, 6},
{6, 11},
{7, 6},
{8, 11},
{9, 6},
{10, 11},
{11, 6},
{12, 11},
{13, 6},
{14, 11},
{15, 6},
{16, 11},
{17, 6},
{18, 11},
{19, 6},
{20, 11}
        },
        burst = {

{1, 11},
{2, 9},
{3, 15},
{4, 29},
{5, 17},
{6, 35},
{7, 17},
{8, 35},
{9, 17},
{10, 35},
{11, 17},
{12, 35},
{13, 17},
{14, 35},
{15, 17},
{16, 35},
{17, 17},
{18, 35},
{19, 17},
{20, 35},
{21, 17},
{22, 0},
{23, 0},
{24, 0},
{25, 0},
{26, 0},
{27, 0},
{28, 0},
{29, 0},
{30, 0},
{31, 0},
{32, 0},
        },
    },
    MK12 = {
        default = {
{1, 5},
{2, 9},
{3, 5},
{4, 9},
{5, 5},
{6, 9},
{7, 5},
{8, 9},
{9, 5},
{10, 9},
{11, 5},
{12, 9},
{13, 5},
{14, 9},
{15, 5},
{16, 9},
{17, 5},
{18, 9},
{19, 5},
{20, 9},
{21, 5},
{22, 9},
{23, 5},
{24, 9},
{25, 5},
{26, 9},
{27, 5},
{28, 9},
{29, 5},
{30, 9},
{31, 9}
        },
        burst = {

{1, 9},
{2, 11},
{3, 16},
{4, 31},
{5, 16},
{6, 31},
{7, 18},
{8, 35},
{9, 18},
{10, 35},
{11, 18},
{12, 35},
{13, 18},
{14, 35},
{15, 18},
{16, 34},
{17, 17},
{18, 34},
{19, 17},
{20, 34},
{21, 17},
{22, 34},
{23, 17},
{24, 34},
{25, 17},
{26, 34},
{27, 17},
{28, 34},
{29, 17},
{30, 34},
{31, 17},
{32, 24},

        },
    },
    MK14 = {
        default = {
{1, 5},
{2, 9},
{3, 5},
{4, 9},
{5, 5},
{6, 9},
{7, 5},
{8, 9},
{9, 5},
{10, 9},
{11, 5},
{12, 9},
{13, 5},
{14, 9},
{15, 5},
{16, 9},
{17, 5},
{18, 9},
{19, 5},
{20, 9}
        },
        burst = {

{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    VSS = {
        default = {
{1, 5},
{2, 9},
{3, 5},
{4, 9},
{5, 5},
{6, 9},
{7, 5},
{8, 9},
{9, 5},
{10, 9},
{11, 5},
{12, 9},
{13, 5},
{14, 9},
{15, 5},
{16, 9},
{17, 5},
{18, 9},
{19, 5},
{20, 9}
        },
        burst = {

{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    DLG = {
        default = {
{1, 9},
{2, 17},
{3, 9},
{4, 17},
{5, 9},
{6, 17},
{7, 9},
{8, 17},
{9, 9},
{10, 17},
{11, 9},
{12, 17},
{13, 9},
{14, 17},
{15, 9},
{16, 17},
{17, 9},
{18, 17},
{19, 9},
{20, 17}

        },
        burst = {

{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },       
    
}


 weapon_intervals = {
  None = 86,
    Berry = 86,
    AUG = 84, 
    AKM = 102, 
    M416 = 87, 
    ACE32 = 89,
    G36C = 87,
    SCAR = 87, 
    QBZ = 87, 
    K2 = 87, 
    M16 = 78, 
    MK47 = 76, 
    GROZA = 80, 
    FAMAS = 67, 
    PP19 = 84, 
    TOM = 80, 
    UMP = 88, 
    UZI = 48, 
    VECTOR = 54, 
    MP5 = 66, 
    P90 = 60, 
    JS9 = 66, 
    MP9 = 60, 
    DP28 = 66, 
    M249 = 75, 
    MG3 = 61, 
    MINI = 108,
    SLR = 108,
    SKS = 108,
    MK12 = 100,
    QBU = 108,
    DLG = 108,
    VSS = 108,
     MK14 = 108,
}

addr = "C:/Temp/weapon.lua"  
 decimal_cache = 0

global_recoil_multiplier = 18.4/ 100 --全局系数  


function table.contains(ax, ay)
    for S, l in pairs(ax) do
        if l == ay then return true end
    end
    return false
end
-- 基础变量定义
local a = 95
local b = 73
local c = 79
local d = false  -- 0 == 1 恒为假
local e = true   -- not d 恒为真（与d相反）
local f = nil
local g = ""
local h = _G  -- 全局环境引用（用于调用全局函数）
local i = _ENV  -- 当前环境
local j = tonumber  -- 数字转换函数（h["\116\111\110\117\109\98\101\114"]还原）


-- 检查表是否包含指定元素（工具函数）
    
    
local k = function(...)
    -- 定义"取整与缓存"函数（处理小数缓存与取整逻辑）
    h["ceil_and_cache"] = function(l)
        local m = math.floor(l)  -- math.floor取整（h["\109\97\116\104"]["\102\108\111\111\114"]还原）
        h["decimal_cache"] = h["decimal_cache"] + l - m  -- 累计小数部分到缓存
        if h["decimal_cache"] >= j("1") then  -- 缓存超过1时进位
            m = m + j("1")
            h["decimal_cache"] = h["decimal_cache"] - j("1")
        end
        return m
    end

    -- 临时变量初始化
    local n = d  -- n = false
    local o = d  -- o = false（调试开关，初始关闭）
    local p = {}  -- 武器状态缓存表
    local q = j("0")  -- 基准时间（初始为0）

    -- 延迟函数（等待指定毫秒数）
    h["Sleep2"] = function(r)
        local s = GetRunningTime()  -- 获取当前运行时间（罗技API）
        while GetRunningTime() - s <= r do end  -- 循环等待至指定时长
    end

    local t = j("0")  -- 临时计数变量

    -- 应用后坐力补偿核心函数（根据武器配置控制鼠标移动）
    h["apply_recoil"] = function(u, v, w, x, y, z, A, B)
        if not is_authenticated() then return end  -- 未授权时直接返回

        -- 武器类型列表（适配后坐力补偿的武器）
        local C = {"MINI", "SKS", "MK12", "SLR", "QBU"}
        -- 检查CapsLock是否锁定（用于切换模式的触发条件）
        local D = IsKeyLockOn("capslock")  -- 罗技API：检查按键锁定状态
        local E = "default"  -- 默认模式
        -- 若CapsLock锁定且当前武器在适配列表中，切换为"burst"模式
        if D and table.contains(C, u) then
            E = "burst"
        end

        -- 读取武器后坐力配置（优先取模式对应的配置，无则取默认）
        local F = h["recoil_patterns"][u] and h["recoil_patterns"][u][E] or h["recoil_patterns"][u]
        -- 读取武器间隔参数（控制补偿频率）
        local G = h["weapon_intervals"][u]
        -- 配置或间隔参数不存在时，输出错误日志并返回
        if not F or not G then
            OutputLogMessage("Failed to load weapon configuration: %s\n", u)  -- 英文提示
            return
        end

        -- 读取附件对后坐力的乘数影响（如枪口、握把等）
        local H = h["attachment_multipliers"][u] and h["attachment_multipliers"][u]["poses"]
        local I = j("1")  -- 初始乘数1
        local J = j("1")
        if H then  -- 若有附件配置，覆盖默认乘数
            I = H[z] or j("1")
            J = H[z] or j("1")
            if D then  -- CapsLock锁定时使用标准爆发模式乘数
                I = H["standBurst"] or I
                J = H["downBurst"] or J
            end
        end

        -- 临时变量初始化
        local K = j("0")  -- 当前帧计数
        local L = j("0")
        local M = j("0")
        local N = B
        -- 特殊武器列表（单独处理逻辑）
        local O = {"MK47", "M16", "None"}
        -- 适配武器列表（与C一致，用于二次判断）
        local P = {"MINI", "SKS", "MK12", "SLR", "QBU"}
        local Q = j("1")  -- 循环次数

        -- 逻辑分支1：特殊武器（O列表中的武器）处理
        if table.contains(O, u) then
            -- 循环条件：左键按下（射击）且右键/侧键按下（触发补偿）
            while IsMouseButtonPressed(1) and (IsMouseButtonPressed(3) or IsMouseButtonPressed(4)) do
                local R = GetRunningTime()  -- 获取当前时间
                -- 计算当前帧（根据运行时间和武器间隔计算）
                K = math.ceil((R - q) / G)
                -- 遍历后坐力配置，匹配当前帧对应的补偿参数
                for S, T in ipairs(F) do  -- ipairs遍历数组（h["\105\112\97\105\114\115"]还原）
                    if T[1] == K then  -- 帧匹配成功
                        z, M = read_poses_file()  -- 读取姿势文件（自定义函数）
                        -- 计算后坐力补偿乘数
                        h["multiplier"] = calculate_recoil_multiplier(u, v, w, x, y, z, A, car, K)
                        -- 计算实际补偿量（结合乘数和缓存取整）
                        local U = h["ceil_and_cache"](T[2] * h["multiplier"])
                        -- 处理X轴移动（自定义函数）
                        local V = handle_x_movement(M)

                        -- 第一帧特殊处理：分多次移动（平滑补偿）
                        if K == j("1") then
                            for W = j("1"), Q do
                                -- 相对移动鼠标（罗技API：补偿后坐力）
                                MoveMouseRelative(V, h["ceil_and_cache"]((U + first_shot_offset) / Q))
                            end
                            PressAndReleaseKey("F8")  -- 按下并释放F8（辅助触发）
                        else
                            -- 非第一帧直接移动
                            MoveMouseRelative(V, U)
                            PressAndReleaseKey("F8")
                        end

                        -- 调试模式：强制移动到(1,0)位置
                        if o then
                            MoveMouseRelative(1, 0)
                        end
                        break  -- 匹配成功后跳出循环
                    end
                end
                Sleep2(j("1"))  -- 延迟1毫秒（控制频率）
                -- 左键松开时重置OLD并跳出循环
                if not IsMouseButtonPressed(1) then
                    OLD = j("0")
                    break
                end
            end

        -- 逻辑分支2：适配武器（P列表）且CapsLock锁定时的处理
        elseif D and table.contains(P, u) then
            while IsMouseButtonPressed(1) and (IsMouseButtonPressed(3) or IsMouseButtonPressed(4)) do
                local R = GetRunningTime()
                K = math.ceil((R - q) / G)
                for S, T in ipairs(F) do
                    if T[1] == K then
                        z, M = read_poses_file()
                        h["multiplier"] = calculate_recoil_multiplier(u, v, w, x, y, z, A, car, K)
                        local U = h["ceil_and_cache"](T[2] * h["multiplier"])
                        local V = handle_x_movement(M)

                        if K == j("1") then
                            for W = j("1"), Q do
                                MoveMouseRelative(V, h["ceil_and_cache"]((U + first_shot_offset) / Q))
                            end
                            PressAndReleaseKey("F8")
                        else
                            MoveMouseRelative(V, U)
                            PressAndReleaseKey("F8")
                        end

                        -- 调试模式：重置OLD并跳出
                        if o then
                            OLD = j("0")
                            break
                        end
                        -- 调试模式：移动到(3,0)位置
                        if o then
                            MoveMouseRelative(3, 0)
                        end
                        break
                    end
                end
                Sleep2(j("1"))
                if not IsMouseButtonPressed(1) then
                    break
                end
            end

        -- 逻辑分支3：默认处理（非特殊/适配武器）
        else
            local X = j("0")
            while IsMouseButtonPressed(1) and (IsMouseButtonPressed(3) or IsMouseButtonPressed(4)) do
                local R = GetRunningTime()
                K = math.ceil((R - q) / G)
                for S, T in ipairs(F) do
                    if T[1] == K then
                        z, M = read_poses_file()
                        h["multiplier"] = calculate_recoil_multiplier(u, v, w, x, y, z, A, car, K)
                        local U = h["ceil_and_cache"](T[2] * h["multiplier"])
                        local V = handle_x_movement(M)

                        if K == j("1") then
                            for W = j("1"), Q do
                                MoveMouseRelative(V, h["ceil_and_cache"]((U + first_shot_offset) / Q))
                            end
                        else
                            MoveMouseRelative(V, U)
                        end

                        -- 左键松开时重置OLD并跳出
                        if not IsMouseButtonPressed(1) then
                            OLD = j("0")
                            break
                        end
                        -- 调试模式：移动到(1,0)位置
                        if o then
                            MoveMouseRelative(1, 0)
                        end
                        break
                    end
                end
                Sleep2(j("1"))
                if not IsMouseButtonPressed(1) then
                    break
                end
            end
        end
    end

    -- 临时变量（存储武器配置）
    local Y = f
    local Z = f
    local _ = f
    local a0 = f
    local a1 = f
    local a2 = f
    local a3 = f
    local a4 = f
    local a5 = f
    local a6 = f

    -- 读取武器配置文件并赋值给临时变量
    local function a7()
        -- 读取武器配置（自定义函数）
        local u, v, w, x, y, z, A, a8, a9, B = h["read_weapon_from_file"]()
        if u then  -- 配置读取成功时赋值
            Y = u; Z = v; _ = w; a0 = x; a1 = y; a2 = z; a3 = A; a4 = a8; a5 = a9; a6 = B
        end
    end

    local aa = j("1")  -- 计数阈值
    local ab = h["GetRunningTime"]()  -- 初始运行时间

    -- 从文件读取武器配置（带权限检查）
    h["read_weapon_from_file"] = function()
        if not is_authenticated() then return f end  -- 未授权返回nil

        -- 初始化武器相关参数
        h["weapon_name"] = f
        h["scopes"] = f
        h["muzzles"] = f
        h["stocks"] = f
        h["poses"] = f
        h["shot"] = f
        h["car"] = f
        h["cursor_x"] = j("0")
        h["dofile"](h["addr"])  -- 执行指定路径的文件（加载配置）

        -- 配置存在时输出日志并返回
        if h["weapon_name"] then
            local ac = string.format("%s+%s+%s+%s+%s+%s+%s+%s+%s",  -- 字符串格式化
                h["weapon_name"], h["muzzles"], h["grips"], h["scopes"], 
                h["stocks"], h["poses"], h["scope_zoom"], h["shot"], h["car"])
            OutputLogMessage("%s\n", ac)  -- 输出配置信息
            return h["weapon_name"], h["muzzles"], h["grips"], h["scopes"], 
                h["stocks"], h["poses"], h["scope_zoom"], h["shot"], h["car"], h["cursor_x"]
        else
            -- 配置不存在时输出错误提示（英文）
            OutputLogMessage("Weapon configuration is empty, cannot load. Please check if the configuration file exists\n")
            return Y, Z, _, a0, a1, a2, a3, a4, a5, a6
        end
    end

    -- 读取姿势文件（带权限检查）
    h["read_poses_file"] = function()
        if not is_authenticated() then return f end  -- 未授权返回nil

        h["poses"] = f
        h["cursor_x"] = j("0")
        h["dofile"](h["addr"])  -- 加载姿势配置

        -- 配置存在时更新缓存，否则使用旧值
        if h["weapon_name"] then
            a2 = h["poses"]
            a6 = h["cursor_x"]
            return h["poses"], h["cursor_x"]
        else
            return a2, a6
        end
    end

    -- 事件处理函数（罗技驱动核心：响应按键/配置事件）
    h["OnEvent"] = function(event, arg)
        -- 配置激活事件：启用鼠标按键事件监听，加载武器配置
        if event == "PROFILE_ACTIVATED" then
            EnablePrimaryMouseButtonEvents(e)  -- 启用鼠标主键事件
            a7()  -- 读取配置
        -- 配置停用事件：禁用鼠标按键事件监听
        elseif event == "PROFILE_DEACTIVATED" then
            EnablePrimaryMouseButtonEvents(d)  -- 禁用鼠标主键事件
        -- 鼠标按键按下事件：处理射击、调试等逻辑
        elseif event == "MOUSE_BUTTON_PRESSED" then
            local af = GetRunningTime()  -- 当前时间
            -- 超过阈值时重新读取配置（避免频繁读取）
            if af - ab >= aa then
                a7()
                ab = af  -- 更新基准时间
            end

            -- 左键按下（射击键）：记录时间，触发后坐力补偿
            if arg == j("1") then
                q = GetRunningTime()  -- 更新基准时间
                PressKey("F8")  -- 按下F8（辅助键）
                if Y then  -- 配置存在时应用补偿
                    h["apply_recoil"](Y, Z, _, a0, a1, a2, a3, a5, a6)
                end
            -- 右键按下（瞄准键）：输出当前配置信息
            elseif arg == j("2") then
                if Y then
                    local ac = string.format("%s+%s+%s+%s+%s+%s+%s+%s",
                        Y, Z, _, a0, a1, a2, a3, a5)
                    OutputLogMessage("%s\n", ac)
                end
            -- 侧键5按下（LShift+侧键）：切换武器开关状态（调试）
            --elseif arg == j("5") and IsModifierPressed("lctrl") then
                --if Y then    
                    --p[Y] = not p[Y]  -- 切换状态（开/关）
                    --OutputLogMessage("Weapon %s status: %s\n", Y, p[Y] and "Enabled" or "Disabled")
                --end
            -- 侧键6按下（LShift+侧键）：切换调试模式（开/关）
            --elseif arg == j("6") and IsModifierPressed("lctrl") then
                --o = not o  -- 切换调试开关
                --OutputLogMessage("Debug mode: %s\n", o and "Enabled" or "Disabled")
            -- 侧键7按下：快速拾取（游戏内功能）
            elseif arg == j("7") then
                h["fastPickup"]()  -- 快速拾取函数
            end
        -- 鼠标按键释放事件：处理按键抬起逻辑
        elseif event == "MOUSE_BUTTON_RELEASED" then
            -- 左键释放：释放F8，重置状态
            if arg == j("1") then
                ReleaseKey("F8")
                h["MoveMouseRelative"](0, 0)  -- 停止鼠标移动
            end      
        end
  
        -- 特殊触发：若按下"pick"键，执行自动拾取
        if event == "MOUSE_BUTTON_PRESSED" and arg == h["pick"] and IsModifierPressed("lctrl") then
            h["autoPick"]()
        end
    end  

    -- 自动拾取函数（快速点击拾取键）
    h["autoPick"] = function()
        PressAndReleaseKey("tab")  -- 按下并释放Tab（拾取键）
        Sleep2(j("50"))  -- 延迟50毫秒

        -- 循环点击（模拟多次点击提升拾取效率）
        for ag = j("1"), j("3") do
            for ah = j("1"), j("3") do
                -- 移动鼠标到指定位置（模拟手动拾取位置）
                MoveMouseTo(j("7800"), j("35000") - ah * j("3423"))
                PressMouseButton(j("1"))  -- 按下左键
                -- 移动鼠标到另一位置
                MoveMouseTo(j("32767") + ah * j("11"), j("12500") + ah * j("12"))
                ReleaseMouseButton(j("1"))  -- 释放左键
                Sleep(j("1"))  -- 延迟1毫秒
            end
        end

        -- 重置鼠标位置
        MoveMouseTo(j("32767"), j("32767"))
        Sleep2(j("1"))
        PressAndReleaseKey("tab")  -- 再次按下Tab（关闭拾取界面）
    end

    -- 鼠标位置检查函数（计算移动补偿）
    local ai = j("0")
    local aj = j("0")
    h["pid_check"] = function(aj)
        if aj == j("0") then
            ai = j("0")
            return j("0")
        end

        local ak = j("0")
        local al = j("0")
        ak = aj * j("1")  -- 比例项
        al = ak * j("0.1") + ai  -- 积分项（带历史累积）
        local am = ak + al  -- 总补偿量

        -- 积分限幅（避免累积过大）
        if al > j("2") then
            ai = j("2")
        elseif al < -j("2") then
            ai = -j("2")
        else
            ai = al  -- 更新积分累积
        end
        return am
    end

    -- 计算后坐力补偿乘数（综合武器、附件、状态等因素）
    h["calculate_recoil_multiplier"] = function(u, v, w, x, y, z, A, a9, K)
        local an = h["global_recoil_multiplier"]  -- 全局乘数
        -- 读取武器附件乘数配置
        local ao = h["attachment_multipliers"][u]
        -- 乘以基础系数（默认1）
        an = an * (h["base_coefficients"][u] or j("1"))

        if ao then  -- 附件配置存在时
            local H = ao["poses"]
            local ap = z
            local aq = {"MINI", "SKS", "MK12", "SLR", "QBU"}  -- 适配武器

            -- CapsLock锁定时切换乘数键（标准/下蹲模式）
            if table.contains(aq, u) and IsKeyLockOn("capslock") then
                ap = (z == "stand") and "standBurst" 
                    or (z == "down") and "downBurst" 
                    or z  -- 默认为原模式
            end

            -- 乘以姿势对应的乘数（默认1）
            an = an * (H[ap] or j("1"))

            -- 子函数：根据配件类型更新乘数（枪口、握把等）
            local function ar(part_type, part_value)
                local au = ao[part_type][part_value]
                -- 若配件是表且包含分段参数，按当前计数匹配乘数
                if type(au) == "table" and au["segments"] then
                    for W, av in ipairs(au["segments"]) do
                        if K <= av["count"] then  -- 计数未超过当前分段
                            an = an * av["multiplier"]
                            return
                        end
                    end
                -- 若配件是数字，直接乘以该值
                elseif type(au) == "number" then
                    an = an * au
                -- 其他情况用默认乘数1
                else
                    an = an * (ao[part_type][part_value] or j("1"))
                end
            end

            -- 分别处理不同配件对乘数的影响
            ar("muzzles", v)  -- 枪口
            ar("grips", w)    -- 握把
            ar("scopes", x)   -- 倍镜
            ar("stocks", y)   -- 枪托
            ar("car", a9)     -- 载具（若有）
        end

        -- 乘以全局倍镜乘数（影响不同倍镜下的补偿强度）
        an = an * (h["global_scope_multipliers"][x] or j("1"))

        -- 特殊武器额外乘数（如某些武器需要增强/减弱补偿）
        local aw = {"Berry", "AUG", "AKM", "M416", "ACE32", "G36C", "SCAR", "QBZ", 
                    "K2", "M16", "MK47", "GROZA", "FAMAS", "PP19", "TOM", "UMP", 
                    "UZ1", "VECTOR", "MP5", "P90", "JS9", "MP7", "M249", "MG3"}
        -- 若当前武器是特殊武器且开启了修改器，应用额外乘数
        if IsModifierPressed("lshift") and table.contains(aw, u) then
            an = an * h["global_breath_multiplier"]
        end

        -- 最终乘以全局灵敏度和垂直灵敏度乘数
        an = an * h["global_sensitivity_multiplier"]
        an = an * h["global_vertical_sensitivity_multiplier"]
        return an
    end

    

    -- 权限检查函数（判断是否已授权）
    h["is_authenticated"] = function()
        local az, aA = pcall(h["dofile"], h["addr"])  -- 尝试加载授权文件
        -- 加载失败时输出错误日志
        if not az then
            OutputLogMessage("Failed to load authorization file: %s\n", aA)
            return d
        end
        -- 授权成功条件：武器配置是字符串且不为"None"
        return type(h["muzzle"]) == "string" and h["muzzle"] ~= "None"
    end

    -- 初始化：遍历后坐力配置，处理偶数索引的参数（平滑补偿）
    for u, aB in pairs(h["recoil_patterns"]) do  -- pairs遍历表（h["\112\97\105\114\115"]还原）
        for E, F in pairs(aB) do  -- 遍历模式（default/burst等）
            for W, aC in ipairs(F) do  -- 遍历帧配置
                if W % j("2") == j("0") then  -- 偶数帧参数处理
                    F[W][2] = (F[W][2] + j("1")) / j("2")  -- 取平均值平滑
                end
            end
        end
    end

    -- 以下为鼠标按键与状态绑定的事件函数（G1-G5键按下/释放）
    h["G1_PRESSED"] = function()
        h["G1___"] = e  -- 标记G1按下状态
        h["OnEvent"]("MOUSE_BUTTON_PRESSED", j("1"), "mouse")  -- 触发左键按下事件
    end
    h["G1_RELEASED"] = function()
        h["G1___"] = d  -- 标记G1释放状态
        h["OnEvent"]("MOUSE_BUTTON_RELEASED", j("1"), "mouse")  -- 触发左键释放事件
    end

    h["G2_PRESSED"] = function()
        h["G2___"] = e
        h["OnEvent"]("MOUSE_BUTTON_PRESSED", j("2"), "mouse")
    end
    h["G2_RELEASED"] = function()
        h["G2___"] = d
        h["OnEvent"]("MOUSE_BUTTON_RELEASED", j("2"), "mouse")
    end

    h["G3_PRESSED"] = function()
        h["G3___"] = e
        h["OnEvent"]("MOUSE_BUTTON_PRESSED", j("3"), "mouse")
    end
    h["G3_RELEASED"] = function()
        h["G3___"] = d
        h["OnEvent"]("MOUSE_BUTTON_RELEASED", j("3"), "mouse")
    end

    h["G4_PRESSED"] = function()
        h["G4___"] = e
        h["OnEvent"]("MOUSE_BUTTON_PRESSED", j("4"), "mouse")
    end
    h["G4_RELEASED"] = function()
        h["G4___"] = d
        h["OnEvent"]("MOUSE_BUTTON_RELEASED", j("4"), "mouse")
    end

    h["G5_PRESSED"] = function()
        h["G5___"] = e
        h["OnEvent"]("MOUSE_BUTTON_PRESSED", j("5"), "mouse")
    end
    h["G5_RELEASED"] = function()
        h["G5___"] = d
        h["OnEvent"]("MOUSE_BUTTON_RELEASED", j("5"), "mouse")
    end

    -- 主循环：监听鼠标按键状态，同步触发对应事件
    while e do  -- 持续运行（e为true）
        -- G1键（左键）状态同步
        while IsMouseButtonPressed(1) and not h["G1___"] do
            h["G1_PRESSED"]()
            break
            Sleep(j("1"))
        end
        while not IsMouseButtonPressed(1) and h["G1___"] do
            h["G1_RELEASED"]()
            break
            Sleep(j("1"))
        end

        -- G3键状态同步
        while IsMouseButtonPressed(3) and not h["G2___"] do
            h["G2_PRESSED"]()
            break
            Sleep(j("1"))
        end
        while not IsMouseButtonPressed(3) and h["G2___"] do
            h["G2_RELEASED"]()
            break
            Sleep(j("1"))
        end

        -- G2键状态同步
        while IsMouseButtonPressed(2) and not h["G3___"] do
            h["G3_PRESSED"]()
            break
            Sleep(j("1"))
        end
        while not IsMouseButtonPressed(2) and h["G3___"] do
            h["G3_RELEASED"]()
            break
            Sleep(j("1"))
        end

        -- G4键状态同步
        while IsMouseButtonPressed(4) and not h["G4___"] do
            h["G4_PRESSED"]()
            break
            Sleep(j("1"))
        end
        while not IsMouseButtonPressed(4) and h["G4___"] do
            h["G4_RELEASED"]()
            break
            Sleep(j("1"))
        end

        -- G5键状态同步
        while IsMouseButtonPressed(5) and not h["G5___"] do
            h["G5_PRESSED"]()
            break
            Sleep(j("1"))
        end
        while not IsMouseButtonPressed(5) and h["G5___"] do
            h["G5_RELEASED"]()
            break
            Sleep(j("1"))
        end

        Sleep(j("1"))  -- 循环延迟1毫秒（降低CPU占用）
    end
end

-- 执行主函数并返回
k = k(...)
return k