{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "WeaponRecognition", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-3b1a860f61a7c3efa751.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "WeaponRecognition::@6890427a1f51a3e7e1df", "jsonFile": "target-WeaponRecognition-Debug-264c10d91c33bfaf8fed.json", "name": "WeaponRecognition", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-a10d608c86c0fcc8c33d.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "WeaponRecognition", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-3b1a860f61a7c3efa751.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "WeaponRecognition::@6890427a1f51a3e7e1df", "jsonFile": "target-WeaponRecognition-Release-4bd15c7033565c382e87.json", "name": "WeaponRecognition", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-a10d608c86c0fcc8c33d.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "WeaponRecognition", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-3b1a860f61a7c3efa751.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "WeaponRecognition::@6890427a1f51a3e7e1df", "jsonFile": "target-WeaponRecognition-MinSizeRel-3f5572037296692fd53c.json", "name": "WeaponRecognition", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-a10d608c86c0fcc8c33d.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "WeaponRecognition", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-3b1a860f61a7c3efa751.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "WeaponRecognition::@6890427a1f51a3e7e1df", "jsonFile": "target-WeaponRecognition-RelWithDebInfo-1cad2d9b419cc74c1256.json", "name": "WeaponRecognition", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-a10d608c86c0fcc8c33d.json", "name": "ZERO_CHECK", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/Temp_code/build", "source": "C:/Users/<USER>/Desktop/Temp_code/cpp_weapon_recognition"}, "version": {"major": 2, "minor": 4}}