#pragma once

#include "Types.h"
#include "TemplateManager.h"
#include <opencv2/opencv.hpp>
#include <vector>
#include <future>
#include <memory>

namespace WeaponRecognition {

class RecognitionEngine {
public:
    explicit RecognitionEngine(std::shared_ptr<TemplateManager> template_manager);
    ~RecognitionEngine() = default;

    // 设置识别阈值
    void setRecognitionThreshold(double threshold) { recognition_threshold_ = threshold; }
    
    // 单个区域识别
    RecognitionResult recognizeRegion(const cv::Mat& image, const std::string& category);
    
    // 批量区域识别（并行优化）
    std::vector<RecognitionResult> recognizeRegions(
        const std::vector<cv::Mat>& images, 
        const std::vector<std::string>& categories
    );
    
    // 武器配置识别
    WeaponConfig recognizeWeaponConfig(
        const std::unordered_map<std::string, cv::Mat>& region_images,
        WeaponType weapon_type
    );
    
    // 快速识别（降低精度换取速度）
    RecognitionResult quickRecognizeRegion(const cv::Mat& image, const std::string& category);
    
    // 姿势识别
    std::string recognizePose(const cv::Mat& image);
    
    // 载具状态识别
    bool recognizeVehicleStatus(const cv::Mat& image);
    
    // 性能统计
    double getAverageRecognitionTime() const { return avg_recognition_time_; }
    double getSuccessRate() const;

private:
    std::shared_ptr<TemplateManager> template_manager_;
    double recognition_threshold_;
    
    // 性能统计
    mutable std::mutex stats_mutex_;
    double avg_recognition_time_;
    int recognition_count_;
    int successful_recognitions_;
    
    // 模板匹配方法
    double matchTemplate(const cv::Mat& image, const cv::Mat& template_img);
    double matchTemplateNormalized(const cv::Mat& image, const cv::Mat& template_img);
    double matchTemplateFast(const cv::Mat& image, const cv::Mat& template_img);
    
    // 图像预处理
    cv::Mat preprocessImage(const cv::Mat& image, bool fast_mode = false);
    cv::Mat enhanceContrast(const cv::Mat& image);
    cv::Mat reduceNoise(const cv::Mat& image);
    
    // 多尺度匹配
    std::vector<double> multiScaleMatch(const cv::Mat& image, const cv::Mat& template_img);
    
    // 结果后处理
    RecognitionResult postProcessResult(const std::string& category, 
                                      const std::string& best_match, 
                                      double confidence);
    
    // 性能优化
    void updateRecognitionStats(double time_ms, bool success);
    
    // 缓存管理
    std::unordered_map<std::string, cv::Mat> preprocessed_cache_;
    std::mutex cache_mutex_;
    void clearCache();
    
    // 并行处理
    std::vector<std::future<RecognitionResult>> processRegionsParallel(
        const std::vector<cv::Mat>& images,
        const std::vector<std::string>& categories
    );
};

} // namespace WeaponRecognition
