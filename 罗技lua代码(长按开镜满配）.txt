EnablePrimaryMouseButtonEvents(true)

-- 定义武器配件的修正系数，每个武器一个表
local attachment_multipliers = {
    None = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,
        },
        
        muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            xy3 = 1,
            bc1 = 1,
            bc2 = 1,
            bc3 = 1,
            xx = 1,
            xx1 = 1,
                   
        },
        grips = {
            None = 1,
            angle = 1, 
            light = 1,
            red = 1,
            line = 1,
            thumb = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 1,
            pg = 1,
        },
    },
    Berry = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.87,
        },
        muzzles = {
            None = 1,
            xy1 = 0.8,
            xy2 = 1,
            xy3 = 1,
            bc1 = 1,
            bc2 = 1,
            bc3 = 1,
            xx = 1,
            xx1 = 1,
                   
        },
        grips = {
            None = 1,
            angle = 1, 
            light = 1,
            red = 1,
            line = 1,
            thumb = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 1,
            pg = 1,
        },
    },
    MK47 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,
        },
        muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            xy3 = 1,
            bc1 = 1,
            bc2 = 1,
            bc3 = 1,
            xx = 1,
            xx1 = 1,
                   
        },
        grips = {
            None = 1,
            angle = 1, 
            light = 1,
            red = 1,
            line = 1,
            thumb = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 1,
            pg = 1,
        },
    },
    MINI = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,
        },
        muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            xy3 = 1,
            bc1 = 1,
            bc2 = 1,
            bc3 = 1,
            xx = 1,
            xx1 = 1,
                   
        },
        grips = {
            None = 1,
            angle = 1, 
            light = 1,
            red = 1,
            line = 1,
            thumb = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 1,
            pg = 1,
        },
    },
   AUG = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,
        },
        muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            xy3 = 1,
            bc1 = 1,
            bc2 = 1,
            bc3 = 1,
            xx = 1,
            xx1 = 1,
                   
        },
        grips = {
            None = 1,
            angle = 1, 
            light = 1,
            red = 1,
            line = 1,
            thumb = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 1,
            pg = 1,
        },
    },
    -- 其他武器的数据自己添加
}

-- 预定义武器的压枪模式
--default是全自动模式的弹道
--burst是连点模式的弹道
local recoil_patterns = {
    Berry = {
        default = {
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
{21, 20},
{22, 20},
{23, 20},
{24, 20},
{25, 20},
{26, 20},
{27, 20},
{28, 20},
{29, 20},
{30, 20},
{31, 20},
{32, 20},
{33, 20},
{34, 20},
{35, 20},
{36, 20},
{37, 20},
{38, 20},
{39, 20},
        },
    },
    AUG = {
        default = {
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
{21, 20},
{22, 20},
{23, 20},
{24, 20},
{25, 20},
{26, 20},
{27, 20},
{28, 20},
{29, 20},
{30, 20},
{31, 20},
{32, 20},
{33, 20},
{34, 20},
{35, 20},
{36, 20},
{37, 20},
{38, 20},
{39, 20},
        },
    },
    MK47 = {
        default = {
 {1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
{21, 20},
{22, 20},
{23, 20},
{24, 20},
{25, 20},
{26, 20},
{27, 20},
{28, 20},
{29, 20},
{30, 20},
{31, 20},
{32, 20},
{33, 20},
{34, 20},
{35, 20},
{36, 20},
{37, 20},
{38, 20},
{39, 20},
{40, 20},
{41, 20},
        },
  },    
    MINI = {
        default = {
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
{21, 20},
{22, 20},
{23, 20},
{24, 20},
{25, 20},
{26, 20},
{27, 20},
{28, 20},
{29, 20},
{30, 20},
{31, 20},
{32, 20},
{33, 20},
{34, 20},
{35, 20},
{36, 20},
{37, 20},
{38, 20},
{39, 20},
{40, 20},
{41, 20},
        },
        
        burst = {
             {1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
{21, 20},
{22, 20},
{23, 20},
{24, 20},
{25, 20},
{26, 20},
{27, 20},
{28, 20},
{29, 20},
{30, 20},
{31, 20},
{32, 20},
{33, 20},
{34, 20},
{35, 20},
{36, 20},
{37, 20},
{38, 20},
{39, 20},
{40, 20},
{41, 20},
        },
    },
    
}
  

-- 定义武器射击间隔
local weapon_intervals = {
    Berry = 87,
    MINI = 30,
    MK47 = 30, 
}

-- 累计小数部分
local decimal_cache = 0

-- 全局压枪系数，初始值为100%，可以通过修改此值调整
local global_recoil_multiplier = 100 / 100 

-- 向上取整并缓存小数部分
function ceil_and_cache(value)
    local integer_part = math.floor(value)
    decimal_cache = decimal_cache + value - integer_part
    if decimal_cache >= 1 then
        integer_part = integer_part + 1
        decimal_cache = decimal_cache - 1
    end
    return integer_part
end

-- 计算修正系数
function calculate_recoil_multiplier(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom)
    local multiplier = global_recoil_multiplier -- 应用全局压枪系数
    local weaponData = attachment_multipliers[weapon_name] 

    if weaponData then
        multiplier = multiplier * (weaponData.poses[poses] or 1)  -- 默认站立姿势
        multiplier = multiplier * (weaponData.muzzles[muzzles] or 1)
        multiplier = multiplier * (weaponData.grips[grips] or 1)
        multiplier = multiplier * (weaponData.scopes[scopes] or 1)
        multiplier = multiplier * (weaponData.stocks[stocks] or 1)
        multiplier = multiplier * scope_zoom
    end

    return multiplier
end

-- 定义全局变量来控制非 MK47/M16 枪械的连点模式
local burstModeEnabled = false
local debugModeEnabled = false -- 调试模式开关

-- 使用一个表来存储每个武器的连点模式状态
local weaponBurstModes = {}

-- 存储鼠标左键按下的时间
local ClickStartTime = 0

-- 应用压枪
function apply_recoil(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom)
    local pattern_type = weaponBurstModes[weapon_name] and "burst" or "default"  -- 根据连点模式选择弹道类型
    local pattern = recoil_patterns[weapon_name] and recoil_patterns[weapon_name][pattern_type] or recoil_patterns[weapon_name]
    local interval = weapon_intervals[weapon_name]

    if not pattern or not interval then
        OutputLogMessage("未找到武器的压枪参数: %s\n", weapon_name)
        return
    end

    local multiplier = calculate_recoil_multiplier(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom)
    local bullet_count = 0

    if weapon_name == "MK47" or weapon_name == "M16"  or weapon_name == "None" then 
        -- MK47 和 M16 特殊处理
        while IsMouseButtonPressed(1) and ( IsMouseButtonPressed(3) or IsMouseButtonPressed(4))do  -- 持续检测鼠标左键是否按下
            local ClickCurrentTime = GetRunningTime()
            bullet_count = math.ceil((ClickCurrentTime - ClickStartTime) / interval)  -- 计算子弹序号

            -- 根据子弹序号查找对应的压枪数据
            for _, recoil_data in ipairs(pattern) do
                if recoil_data[1] == bullet_count then
                    local adjusted_recoil = ceil_and_cache(recoil_data[2] * multiplier)
                    MoveMouseRelative(0, adjusted_recoil)
                    PressAndReleaseKey("F8")
                    if not IsMouseButtonPressed(1) then
                        break
                    end
                    if debugModeEnabled then
                        MoveMouseRelative(8, 0) -- 调试模式下水平移动
                    end
                    break  -- 找到对应的数据后跳出循环
                end
            end

            Sleep(1)  -- 适当的延迟，避免 CPU 占用过高
        end
    elseif weaponBurstModes[weapon_name] then 
        -- 其他枪械在开启连点模式后才进行连点压枪
        while IsMouseButtonPressed(1) and ( IsMouseButtonPressed(3) or IsMouseButtonPressed(4)) do  -- 持续检测鼠标左键是否按下
            local ClickCurrentTime = GetRunningTime()
            bullet_count = math.ceil((ClickCurrentTime - ClickStartTime) / interval)  -- 计算子弹序号

            -- 根据子弹序号查找对应的压枪数据
            for _, recoil_data in ipairs(pattern) do
                if recoil_data[1] == bullet_count then
                    local adjusted_recoil = ceil_and_cache(recoil_data[2] * multiplier)
                    MoveMouseRelative(0, adjusted_recoil)
                    PressAndReleaseKey("F8")
                    if not IsMouseButtonPressed(1) then
                        break
                    end
                    if debugModeEnabled then
                        MoveMouseRelative(10, 0) -- 调试模式下水平移动
                    end
                    break  -- 找到对应的数据后跳出循环
                end
            end
            Sleep(1)  -- 适当的延迟，避免 CPU 占用过高
        end
    else
        -- 其他枪械的默认压枪处理（不连点）
        while IsMouseButtonPressed(1) and ( IsMouseButtonPressed(3) or IsMouseButtonPressed(4)) do  -- 持续检测鼠标左键是否按下
            local ClickCurrentTime = GetRunningTime()
            bullet_count = math.ceil((ClickCurrentTime - ClickStartTime) / interval)  -- 计算子弹序号

            -- 根据子弹序号查找对应的压枪数据
            for _, recoil_data in ipairs(pattern) do
                if recoil_data[1] == bullet_count then
                    local adjusted_recoil = ceil_and_cache(recoil_data[2] * multiplier)
                    MoveMouseRelative(0, adjusted_recoil)
                    if not IsMouseButtonPressed(1) then
                        break
                   end
                    if debugModeEnabled then
                        MoveMouseRelative(8, 0) -- 调试模式下水平移动
                    end
                    break  -- 找到对应的数据后跳出循环
                end
            end

            Sleep(1)  -- 适当的延迟，避免 CPU 占用过高
        end
    end
end

-- 存储上一次读取到的武器信息
local last_weapon_name = nil
local last_muzzles = nil
local last_grips = nil
local last_scopes = nil
local last_stocks = nil
local last_poses = nil
local last_scope_zoom = nil

-- 读取武器信息
function read_weapon_from_file()
    weapon_name = nil
    scopes = nil
    poses = nil -- 添加 poses 变量

    dofile("C:/Users/<USER>/Desktop/Temp_code/weapon.lua")

    if weapon_name then
        -- 更新上一次读取到的武器信息
        last_weapon_name = weapon_name
        last_muzzles = muzzles
        last_grips = grips
        last_scopes = scopes
        last_stocks = stocks
        last_poses = poses
        last_scope_zoom = scope_zoom

        local output = string.format("%s+%s+%s+%s+%s+%s+%s",weapon_name, muzzles, grips, scopes, stocks, poses , scope_zoom)
        OutputLogMessage("%s\n", output)
        return weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom
    else
        OutputLogMessage("未找到武器信息, 使用上一次的武器信息\n")
        -- 返回上一次读取到的武器信息
        return last_weapon_name, last_muzzles, last_grips, last_scopes, last_stocks, last_poses, last_scope_zoom
    end
end

-- 事件处理函数
function OnEvent(event, arg)
    if event == "MOUSE_BUTTON_PRESSED" then
        if arg == 1 then
            ClickStartTime = GetRunningTime()  -- 记录开火起始时间
            PressKey("F8")
            local weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom = read_weapon_from_file()
            if weapon_name then
                apply_recoil(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom)
            end
        elseif arg == 2 then  -- 右键按下
            local weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom = read_weapon_from_file()
            if weapon_name then
                local output = string.format("%s+%s+%s+%s+%s+%s+%s",weapon_name, muzzles, grips, scopes, stocks, poses , scope_zoom)
                OutputLogMessage("%s\n", output)
            end
        elseif arg == 5 and IsModifierPressed("lctrl") then
            -- 切换当前武器的连点模式
            local weapon_name = read_weapon_from_file() -- 获取当前武器名称
            if weapon_name then
                weaponBurstModes[weapon_name] = not weaponBurstModes[weapon_name]
                OutputLogMessage("武器 %s 的连点模式 %s\n", weapon_name, weaponBurstModes[weapon_name] and "已开启" or "已关闭")
            end
        elseif arg == 4 and IsModifierPressed("lctrl") then
            debugModeEnabled = not debugModeEnabled
            OutputLogMessage("调试模式%s\n", debugModeEnabled and "已开启" or "已关闭")
        end
    elseif event == "MOUSE_BUTTON_RELEASED" then
        if arg == 1 then
            ReleaseKey("F8")
            MoveMouseRelative(0, 0)
        end
    end

if (event == "MOUSE_BUTTON_PRESSED" and arg == pick and IsModifierPressed("lctrl")) then
        autopick()
    end
end

pick = 3
autopick = function()
    PressAndReleaseKey("tab")
    Sleep(50)
    for k=1,3 do 
        for j=1,5 do 
            MoveMouseTo(7800, (35000 - j * 5425)) 
            PressMouseButton(1) 
            MoveMouseTo(32767 + j * 11, 12500 + j * 12) 
            ReleaseMouseButton(1) 
            Sleep(50) 
        end 
    end   
    MoveMouseTo(32767, 32767)
    Sleep(50)
    PressAndReleaseKey("tab")
end
