#include "RecognitionEngine.h"
#include "Utils.h"
#include <algorithm>
#include <future>
#include <iostream>
#include <iomanip>

namespace WeaponRecognition {

RecognitionEngine::RecognitionEngine(std::shared_ptr<TemplateManager> template_manager)
    : template_manager_(template_manager)
    , recognition_threshold_(0.6)
    , avg_recognition_time_(0.0)
    , recognition_count_(0)
    , successful_recognitions_(0) {
}

RecognitionResult RecognitionEngine::recognizeRegion(const cv::Mat& image, const std::string& category) {
    Utils::Timer timer;
    
    if (image.empty()) {
        return RecognitionResult();
    }
    
    const auto& templates = template_manager_->getTemplates(category);
    if (templates.empty()) {
        std::cout << "⚠️ " << category << ": 没有找到模板" << std::endl;
        return RecognitionResult();
    }
    
    std::string best_match;
    double best_confidence = 0.0;
    
    // 预处理输入图像
    cv::Mat processed_image = preprocessImage(image);
    
    // 遍历所有模板进行匹配
    for (const auto& [template_name, template_img] : templates) {
        if (template_img.empty()) continue;
        
        double confidence = matchTemplate(processed_image, template_img);
        
        if (confidence > best_confidence && confidence > recognition_threshold_) {
            best_confidence = confidence;
            best_match = template_name;
        }
    }
    
    double elapsed = timer.elapsed();
    bool success = !best_match.empty();
    updateRecognitionStats(elapsed, success);
    
    if (success) {
        std::cout << "✅ " << category << ": 识别到 " << best_match 
                  << " (置信度: " << std::fixed << std::setprecision(3) << best_confidence << ")" << std::endl;
    } else {
        std::cout << "⚠️ " << category << ": 未识别到匹配项" << std::endl;
    }
    
    return RecognitionResult(category, best_match, best_confidence);
}

std::vector<RecognitionResult> RecognitionEngine::recognizeRegions(
    const std::vector<cv::Mat>& images, 
    const std::vector<std::string>& categories) {
    
    if (images.size() != categories.size()) {
        return {};
    }
    
    std::vector<RecognitionResult> results;
    results.reserve(images.size());
    
    // 并行识别（如果图像数量较多）
    if (images.size() > 3) {
        auto futures = processRegionsParallel(images, categories);
        for (auto& future : futures) {
            results.emplace_back(future.get());
        }
    } else {
        // 串行识别（避免小任务的线程开销）
        for (size_t i = 0; i < images.size(); ++i) {
            results.emplace_back(recognizeRegion(images[i], categories[i]));
        }
    }
    
    return results;
}

WeaponConfig RecognitionEngine::recognizeWeaponConfig(
    const std::unordered_map<std::string, cv::Mat>& region_images,
    WeaponType weapon_type) {
    
    WeaponConfig config;
    
    // 根据武器类型选择对应的区域
    std::string weapon_key = (weapon_type == WeaponType::RIFLE) ? "weapon_name_rifle" : "weapon_name_sniper";
    std::string muzzle_key = (weapon_type == WeaponType::RIFLE) ? "muzzles_rifle" : "muzzles_sniper";
    std::string grip_key = (weapon_type == WeaponType::RIFLE) ? "grips_rifle" : "grips_sniper";
    std::string scope_key = (weapon_type == WeaponType::RIFLE) ? "scopes_rifle" : "scopes_sniper";
    std::string stock_key = (weapon_type == WeaponType::RIFLE) ? "stocks_rifle" : "stocks_sniper";
    
    // 识别各个组件
    if (region_images.count(weapon_key)) {
        auto result = recognizeRegion(region_images.at(weapon_key), "weapons");
        config.weapon_name = result.result;
    }
    
    if (region_images.count(muzzle_key)) {
        auto result = recognizeRegion(region_images.at(muzzle_key), "muzzles");
        config.muzzles = result.result;
    }
    
    if (region_images.count(grip_key)) {
        auto result = recognizeRegion(region_images.at(grip_key), "grips");
        config.grips = result.result;
    }
    
    if (region_images.count(scope_key)) {
        auto result = recognizeRegion(region_images.at(scope_key), "scopes");
        config.scopes = result.result;
        
        // 设置默认缩放值
        if (config.scopes == "x6" || config.scopes == "x8") {
            config.scope_zoom = 1.6; // 高倍镜默认最大缩放
        } else {
            config.scope_zoom = 1.0; // 其他倍镜固定1.0
        }
    }
    
    if (region_images.count(stock_key)) {
        auto result = recognizeRegion(region_images.at(stock_key), "stocks");
        config.stocks = result.result;
    }
    
    // 识别姿势
    if (region_images.count("poses")) {
        config.poses = recognizePose(region_images.at("poses"));
    }
    
    // 识别载具状态
    if (region_images.count("car")) {
        bool in_vehicle = recognizeVehicleStatus(region_images.at("car"));
        config.car = in_vehicle ? "car" : "None";
    }
    
    return config;
}

RecognitionResult RecognitionEngine::quickRecognizeRegion(const cv::Mat& image, const std::string& category) {
    // 快速识别模式：降低精度换取速度
    if (image.empty()) {
        return RecognitionResult();
    }
    
    const auto& templates = template_manager_->getTemplates(category);
    if (templates.empty()) {
        return RecognitionResult();
    }
    
    std::string best_match;
    double best_confidence = 0.0;
    
    // 快速预处理（跳过降噪等耗时操作）
    cv::Mat processed_image = preprocessImage(image, true);
    
    // 只匹配前几个最常见的模板（性能优化）
    int max_templates = std::min(5, static_cast<int>(templates.size()));
    int count = 0;
    
    for (const auto& [template_name, template_img] : templates) {
        if (++count > max_templates) break;
        if (template_img.empty()) continue;
        
        double confidence = matchTemplateFast(processed_image, template_img);
        
        if (confidence > best_confidence && confidence > (recognition_threshold_ * 0.8)) {
            best_confidence = confidence;
            best_match = template_name;
        }
    }
    
    return RecognitionResult(category, best_match, best_confidence);
}

std::string RecognitionEngine::recognizePose(const cv::Mat& image) {
    auto result = recognizeRegion(image, "poses");
    return result.result.empty() ? "None" : result.result;
}

bool RecognitionEngine::recognizeVehicleStatus(const cv::Mat& image) {
    auto result = recognizeRegion(image, "car");
    return !result.result.empty();
}

double RecognitionEngine::getSuccessRate() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return recognition_count_ > 0 ? 
           static_cast<double>(successful_recognitions_) / recognition_count_ : 0.0;
}

double RecognitionEngine::matchTemplate(const cv::Mat& image, const cv::Mat& template_img) {
    if (image.empty() || template_img.empty()) {
        return 0.0;
    }
    
    cv::Mat result;
    cv::matchTemplate(image, template_img, result, cv::TM_CCOEFF_NORMED);
    
    double min_val, max_val;
    cv::minMaxLoc(result, &min_val, &max_val);
    
    return max_val;
}

double RecognitionEngine::matchTemplateNormalized(const cv::Mat& image, const cv::Mat& template_img) {
    // 标准化模板匹配，对光照变化更鲁棒
    cv::Mat result;
    cv::matchTemplate(image, template_img, result, cv::TM_CCORR_NORMED);
    
    double min_val, max_val;
    cv::minMaxLoc(result, &min_val, &max_val);
    
    return max_val;
}

double RecognitionEngine::matchTemplateFast(const cv::Mat& image, const cv::Mat& template_img) {
    // 快速匹配：使用较低精度但更快的方法
    cv::Mat result;
    cv::matchTemplate(image, template_img, result, cv::TM_SQDIFF_NORMED);
    
    double min_val, max_val;
    cv::minMaxLoc(result, &min_val, &max_val);
    
    return 1.0 - min_val; // SQDIFF_NORMED: 越小越好，所以用1减去
}

cv::Mat RecognitionEngine::preprocessImage(const cv::Mat& image, bool fast_mode) {
    if (image.empty()) {
        return cv::Mat();
    }
    
    cv::Mat processed;
    
    // 转换为灰度图
    if (image.channels() == 3) {
        cv::cvtColor(image, processed, cv::COLOR_BGR2GRAY);
    } else if (image.channels() == 4) {
        cv::cvtColor(image, processed, cv::COLOR_BGRA2GRAY);
    } else {
        processed = image.clone();
    }
    
    if (!fast_mode) {
        // 高质量模式：增强对比度和降噪
        processed = enhanceContrast(processed);
        processed = reduceNoise(processed);
    }
    
    return processed;
}

cv::Mat RecognitionEngine::enhanceContrast(const cv::Mat& image) {
    cv::Mat enhanced;
    cv::equalizeHist(image, enhanced);
    return enhanced;
}

cv::Mat RecognitionEngine::reduceNoise(const cv::Mat& image) {
    cv::Mat denoised;
    cv::medianBlur(image, denoised, 3);
    return denoised;
}

std::vector<double> RecognitionEngine::multiScaleMatch(const cv::Mat& image, const cv::Mat& template_img) {
    std::vector<double> confidences;
    std::vector<double> scales = {0.8, 0.9, 1.0, 1.1, 1.2};
    
    for (double scale : scales) {
        cv::Mat scaled_template;
        cv::resize(template_img, scaled_template, cv::Size(), scale, scale);
        
        if (scaled_template.rows <= image.rows && scaled_template.cols <= image.cols) {
            double confidence = matchTemplate(image, scaled_template);
            confidences.push_back(confidence);
        }
    }
    
    return confidences;
}

RecognitionResult RecognitionEngine::postProcessResult(const std::string& category, 
                                                      const std::string& best_match, 
                                                      double confidence) {
    // 后处理：可以添加结果验证、置信度调整等逻辑
    return RecognitionResult(category, best_match, confidence);
}

void RecognitionEngine::updateRecognitionStats(double time_ms, bool success) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    recognition_count_++;
    if (success) {
        successful_recognitions_++;
    }
    
    avg_recognition_time_ = (avg_recognition_time_ * (recognition_count_ - 1) + time_ms) / recognition_count_;
}

void RecognitionEngine::clearCache() {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    preprocessed_cache_.clear();
}

std::vector<std::future<RecognitionResult>> RecognitionEngine::processRegionsParallel(
    const std::vector<cv::Mat>& images,
    const std::vector<std::string>& categories) {
    
    std::vector<std::future<RecognitionResult>> futures;
    futures.reserve(images.size());
    
    for (size_t i = 0; i < images.size(); ++i) {
        futures.emplace_back(std::async(std::launch::async, [this, &images, &categories, i]() {
            return recognizeRegion(images[i], categories[i]);
        }));
    }
    
    return futures;
}

} // namespace WeaponRecognition
