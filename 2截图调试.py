import cv2
import numpy as np
import mss
import os
import keyboard
import threading
import time

# 指定截取区域的坐标和大小
x, y, width, height = 1356, 242, 20, 20

# 指定存储路径
save_dir = "C:/Users/<USER>/Desktop/Temp/picture/"
if not os.path.exists(save_dir):
    os.makedirs(save_dir)

# 截图标志
is_capturing = False

# 截图回调函数
def capture_screenshot(event):
    global is_capturing
    
    # 检查是否同时按下Ctrl、Alt和S键
    if event.event_type == keyboard.KEY_DOWN and event.name in ["ctrl", "s"]:
        if keyboard.is_pressed("ctrl") and keyboard.is_pressed("s"):
            is_capturing = True
            
            # 在单独的线程中执行截图操作
            threading.Thread(target=do_capture_screenshot).start()

def do_capture_screenshot():
    global is_capturing
    
    with mss.mss() as sct:
        # 截取指定区域
        img = np.array(sct.grab({"left": x, "top": y, "width": width, "height": height}))

        # 生成唯一的文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(save_dir, f"WUQI_{timestamp}.png")

        # 保存截图
        cv2.imwrite(save_path, img)
        print(f"截图已保存至: {save_path}")

        # 显示截图
        cv2.imshow('Captured Screenshot', img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
    is_capturing = False

# 创建MSS对象
print("按下 'Ctrl+S' 开始截图")
keyboard.hook(capture_screenshot)

# 保持程序运行
while True:
    pass
