#pragma once

#include "Types.h"
#include <unordered_map>
#include <string>
#include <opencv2/opencv.hpp>

namespace WeaponRecognition {

class TemplateManager {
public:
    TemplateManager() = default;
    ~TemplateManager() = default;

    // 加载模板
    bool loadTemplates(const std::string& template_path);
    
    // 获取模板
    const TemplateMap& getTemplates(const std::string& category) const;
    
    // 检查模板是否存在
    bool hasTemplate(const std::string& category, const std::string& name) const;
    
    // 获取模板数量
    size_t getTemplateCount() const;

private:
    std::unordered_map<std::string, TemplateMap> templates_;
    
    // 内部方法
    bool loadCategoryTemplates(const std::string& category_path, const std::string& category_name);
    cv::Mat loadTemplate(const std::string& file_path);
};

} // namespace WeaponRecognition
