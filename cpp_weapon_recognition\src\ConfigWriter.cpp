#include "ConfigWriter.h"
#include "Utils.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <iostream>

namespace WeaponRecognition {

bool ConfigWriter::initialize(const std::string& output_path) {
    output_path_ = output_path;
    
    // 确保输出目录存在
    if (!ensureDirectoryExists(output_path)) {
        std::cerr << "❌ 无法创建输出目录: " << output_path << std::endl;
        return false;
    }
    
    std::cout << "✅ 配置写入器初始化完成: " << output_path << std::endl;
    return true;
}

bool ConfigWriter::writeWeaponConfig(const WeaponConfig& config) {
    std::lock_guard<std::mutex> lock(write_mutex_);
    
    std::string content = formatConfig(config);
    return atomicWrite(content);
}

bool ConfigWriter::atomicWrite(const std::string& content) {
    try {
        // 原子写入：先写临时文件，再重命名
        std::string temp_path = output_path_ + ".tmp";
        
        {
            std::ofstream file(temp_path, std::ios::out | std::ios::trunc);
            if (!file.is_open()) {
                std::cerr << "❌ 无法创建临时文件: " << temp_path << std::endl;
                return false;
            }
            
            file << content;
            file.flush();
            
            if (file.fail()) {
                std::cerr << "❌ 写入临时文件失败" << std::endl;
                return false;
            }
        }
        
        // 原子重命名
        if (std::filesystem::exists(output_path_)) {
            std::filesystem::remove(output_path_);
        }
        std::filesystem::rename(temp_path, output_path_);
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 原子写入失败: " << e.what() << std::endl;
        return false;
    }
}

std::string ConfigWriter::formatConfig(const WeaponConfig& config) {
    std::ostringstream oss;
    
    // 格式化武器配置为Lua格式
    oss << "weapon_name = \"" << config.weapon_name << "\"\n";
    oss << "muzzles = \"" << config.muzzles << "\"\n";
    oss << "muzzle = \"None\"\n";  // 固定为None（授权限制）
    oss << "grips = \"" << config.grips << "\"\n";
    oss << "scopes = \"" << config.scopes << "\"\n";
    oss << "stocks = \"" << config.stocks << "\"\n";
    oss << "poses = \"" << config.poses << "\"\n";
    oss << "scope_zoom = \"" << std::fixed << std::setprecision(2) << config.scope_zoom << "\"\n";
    oss << "bag = \"none\"\n";
    oss << "car = \"" << config.car << "\"\n";
    oss << "shoot = \"None\"\n";
    oss << "cursor_x = 0\n";
    
    return oss.str();
}

bool ConfigWriter::ensureDirectoryExists(const std::string& file_path) {
    try {
        std::filesystem::path path(file_path);
        std::filesystem::path dir = path.parent_path();
        
        if (!dir.empty() && !std::filesystem::exists(dir)) {
            return std::filesystem::create_directories(dir);
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "❌ 创建目录失败: " << e.what() << std::endl;
        return false;
    }
}

} // namespace WeaponRecognition
