#pragma once

#include "Types.h"
#include <string>
#include <memory>

namespace WeaponRecognition {

class ConfigManager {
public:
    ConfigManager() = default;
    ~ConfigManager() = default;

    // 加载配置
    bool loadConfig(int screen_width, int screen_height);
    
    // 获取系统配置
    const SystemConfig& getSystemConfig() const { return system_config_; }
    
    // 获取区域配置
    const RegionMap& getRegions() const { return system_config_.regions; }
    
    // 保存配置
    bool saveConfig(const std::string& config_path);

private:
    SystemConfig system_config_;
    std::string config_file_path_;
    
    // 内部方法
    bool loadFromFile(const std::string& file_path);
    std::string findConfigFile(int width, int height);
    bool parseJsonConfig(const std::string& json_content);
};

} // namespace WeaponRecognition
