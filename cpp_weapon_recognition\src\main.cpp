#include "WeaponRecognitionSystem.h"
#include <iostream>
#include <windows.h>
#include <signal.h>

using namespace WeaponRecognition;

// 全局系统实例
std::unique_ptr<WeaponRecognitionSystem> g_system;

// 信号处理函数
void signalHandler(int signal) {
    if (g_system) {
        std::cout << "\n正在安全退出程序..." << std::endl;
        g_system->stop();
        g_system.reset();
    }
    exit(0);
}

// 控制台事件处理
BOOL WINAPI consoleHandler(DWORD dwType) {
    switch (dwType) {
    case CTRL_C_EVENT:
    case CTRL_BREAK_EVENT:
    case CTRL_CLOSE_EVENT:
        signalHandler(SIGINT);
        return TRUE;
    default:
        return FALSE;
    }
}

void printBanner() {
    std::cout << R"(
╔══════════════════════════════════════════════════════════════╗
║                    PUBG武器自动识别系统 C++版                    ║
║                     高性能 | 低延迟 | 稳定                      ║
╠══════════════════════════════════════════════════════════════╣
║  版本: 2.0.0                                                 ║
║  语言: C++17                                                 ║
║  优化: AVX2 + 多线程 + 内存池                                  ║
╚══════════════════════════════════════════════════════════════╝
)" << std::endl;
}

void printUsage() {
    std::cout << "\n使用说明:" << std::endl;
    std::cout << "  ` (波浪号键) - 触发武器识别" << std::endl;
    std::cout << "  1 键 - 切换到步枪配置" << std::endl;
    std::cout << "  2 键 - 切换到狙击枪配置" << std::endl;
    std::cout << "  右键+滚轮 - 调整高倍镜缩放 (x6/x8倍镜)" << std::endl;
    std::cout << "  ESC 键 - 退出程序" << std::endl;
    std::cout << "  Ctrl+C - 强制退出" << std::endl;
    std::cout << "\n工作流程:" << std::endl;
    std::cout << "  1. 进入游戏，确保武器界面可见" << std::endl;
    std::cout << "  2. 按 ` 键识别当前两把武器的配置" << std::endl;
    std::cout << "  3. 按 1 或 2 键选择要使用的武器配置" << std::endl;
    std::cout << "  4. 对于x6/x8倍镜，可按住右键+滚轮调整缩放" << std::endl;
    std::cout << "  5. 罗技脚本会自动读取配置进行压枪" << std::endl;
}

int main() {
    // 设置控制台编码为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    
    // 打印横幅
    printBanner();
    
    try {
        // 注册信号处理
        signal(SIGINT, signalHandler);
        signal(SIGTERM, signalHandler);
        SetConsoleCtrlHandler(consoleHandler, TRUE);
        
        // 创建系统实例
        g_system = std::make_unique<WeaponRecognitionSystem>();
        
        std::cout << "正在初始化系统..." << std::endl;
        
        // 初始化系统
        if (!g_system->initialize()) {
            std::cerr << "❌ 系统初始化失败！" << std::endl;
            return -1;
        }
        
        std::cout << "✅ 系统初始化完成！" << std::endl;
        printUsage();
        
        // 运行系统
        std::cout << "\n🎮 系统正在运行，等待按键操作..." << std::endl;
        std::cout << "💡 提示: 程序已优化为低资源占用模式" << std::endl;
        std::cout << "📊 预期性能: CPU占用 < 5%, 内存占用 < 30MB" << std::endl;
        g_system->run();
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "❌ 未知异常！" << std::endl;
        return -1;
    }
    
    return 0;
}
