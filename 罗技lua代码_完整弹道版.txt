EnablePrimaryMouseButtonEvents(true)

-- Define weapon attachment multipliers, one table per weapon
local attachment_multipliers = {
    None = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,
        },
        
        muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            xy3 = 1,
            bc1 = 1,
            bc2 = 1,
            bc3 = 1,
            xx = 1,
            xx1 = 1,
                   
        },
        grips = {
            None = 1,
            angle = 1, 
            light = 1,
            red = 1,
            line = 1,
            thumb = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 1,
            pg = 1,
        },
    },
    Berry = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.87,
        },
        muzzles = {
            None = 1,
            xy1 = 0.8,
            xy2 = 1,
            xy3 = 1,
            bc1 = 1,
            bc2 = 1,
            bc3 = 1,
            xx = 1,
            xx1 = 1,
                   
        },
        grips = {
            None = 1,
            angle = 1, 
            light = 1,
            red = 1,
            line = 1,
            thumb = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 1,
            pg = 1,
        },
    },
    MK47 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,
        },
        muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            xy3 = 1,
            bc1 = 1,
            bc2 = 1,
            bc3 = 1,
            xx = 1,
            xx1 = 1,
                   
        },
        grips = {
            None = 1,
            angle = 1, 
            light = 1,
            red = 1,
            line = 1,
            thumb = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 1,
            pg = 1,
        },
    },
    MINI = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,
        },
        muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            xy3 = 1,
            bc1 = 1,
            bc2 = 1,
            bc3 = 1,
            xx = 1,
            xx1 = 1,
                   
        },
        grips = {
            None = 1,
            angle = 1, 
            light = 1,
            red = 1,
            line = 1,
            thumb = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 1,
            pg = 1,
        },
    },
    -- Add other weapons data here
}

-- Complete weapon recoil patterns from the full ballistics file
-- default is full auto mode recoil
-- burst is burst mode recoil
local recoil_patterns = {
Berry = {
        default = {
{1, 18},
{2, 29},
{3, 17},
{4, 39},
{5, 21},
{6, 41},
{7, 21},
{8, 45},
{9, 25},
{10, 49},
{11, 25},
{12, 49},
{13, 25},
{14, 49},
{15, 29},
{16, 57},
{17, 30},
{18, 59},
{19, 30},
{20, 59},
{21, 30},
{22, 59},
{23, 30},
{24, 59},
{25, 30},
{26, 59},
{27, 30},
{28, 59},
{29, 30},
{30, 59},
{31, 30},
{32, 59},
{33, 30},
{34, 59},
{35, 30},
{36, 59},
{37, 30},
{38, 59},
{39, 30},
{40, 59},
{41, 30}
        },
    },
None = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 45},
{31, 23},
{32, 45},
{33, 23},
{34, 45},
{35, 23},
{36, 45},
{37, 23},
{38, 45},
{39, 23},
{40, 45},
{41, 23}
        },
    },
    

    AUG = {
        default = {
{1, 24},
{2, 17},
{3, 9},
{4, 25},
{5, 16},
{6, 35},
{7, 20},
{8, 39},
{9, 20},
{10, 41},
{11, 23},
{12, 49},
{13, 25},
{14, 49},
{15, 26},
{16, 51},
{17, 27},
{18, 53},
{19, 27},
{20, 53},
{21, 27},
{22, 53},
{23, 27},
{24, 53},
{25, 27},
{26, 53},
{27, 27},
{28, 53},
{29, 27},
{30, 53},
{31, 27},
{32, 55},
{33, 28},
{34, 55},
{35, 28},
{36, 55},
{37, 28},
{38, 55},
{39, 28},
{40, 55},
{41, 28}
        },
    },

    AKM = {
        default = {
{1, 16},
{2, 25},
{3, 13},
{4, 23},
{5, 16},
{6, 31},
{7, 16},
{8, 35},
{9, 20},
{10, 39},
{11, 21},
{12, 41},
{13, 21},
{14, 41},
{15, 21},
{16, 43},
{17, 22},
{18, 43},
{19, 22},
{20, 43},
{21, 22},
{22, 43},
{23, 22},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 43},
{31, 22},
{32, 43},
{33, 22},
{34, 43},
{35, 22},
{36, 43},
{37, 22},
{38, 43},
{39, 22},
{40, 43},
{41, 22}
        },
    },

    M416 = {
        default = {
{1, 23},
{2, 13},
{3, 7},
{4, 21},
{5, 12},
{6, 29},
{7, 15},
{8, 33},
{9, 17},
{10, 35},
{11, 18},
{12, 37},
{13, 18},
{14, 37},
{15, 18},
{16, 37},
{17, 18},
{18, 37},
{19, 18},
{20, 37},
{21, 18},
{22, 37},
{23, 18},
{24, 37},
{25, 18},
{26, 37},
{27, 18},
{28, 37},
{29, 18},
{30, 37},
{31, 18},
{32, 37},
{33, 18},
{34, 37},
{35, 18},
{36, 37},
{37, 18},
{38, 37},
{39, 18},
{40, 37},
{41, 18}
        },
    },

    SCAR = {
        default = {
{1, 22},
{2, 15},
{3, 9},
{4, 23},
{5, 14},
{6, 31},
{7, 17},
{8, 35},
{9, 18},
{10, 37},
{11, 19},
{12, 39},
{13, 19},
{14, 39},
{15, 19},
{16, 39},
{17, 19},
{18, 39},
{19, 19},
{20, 39},
{21, 19},
{22, 39},
{23, 19},
{24, 39},
{25, 19},
{26, 39},
{27, 19},
{28, 39},
{29, 19},
{30, 39},
{31, 19},
{32, 39},
{33, 19},
{34, 39},
{35, 19},
{36, 39},
{37, 19},
{38, 39},
{39, 19},
{40, 39},
{41, 19}
        },
    },

    QBZ = {
        default = {
{1, 21},
{2, 15},
{3, 9},
{4, 23},
{5, 14},
{6, 31},
{7, 17},
{8, 35},
{9, 18},
{10, 37},
{11, 19},
{12, 39},
{13, 19},
{14, 39},
{15, 19},
{16, 39},
{17, 19},
{18, 39},
{19, 19},
{20, 39},
{21, 19},
{22, 39},
{23, 19},
{24, 39},
{25, 19},
{26, 39},
{27, 19},
{28, 39},
{29, 19},
{30, 39},
{31, 19},
{32, 39},
{33, 19},
{34, 39},
{35, 19},
{36, 39},
{37, 19},
{38, 39},
{39, 19},
{40, 39},
{41, 19}
        },
    },
