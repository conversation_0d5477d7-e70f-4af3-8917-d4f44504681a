^C:\USERS\<USER>\DESKTOP\TEMP_CODE\CPP_WEAPON_RECOGNITION\CMAKELISTS.TXT
setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/Temp_code/cpp_weapon_recognition -BC:/Users/<USER>/Desktop/Temp_code/cpp_weapon_recognition/build --check-stamp-file C:/Users/<USER>/Desktop/Temp_code/cpp_weapon_recognition/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
