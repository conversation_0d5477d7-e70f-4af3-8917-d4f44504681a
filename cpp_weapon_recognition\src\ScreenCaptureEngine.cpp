#include "ScreenCaptureEngine.h"
#include <chrono>
#include <algorithm>
#include <thread>
#include <future>

namespace WeaponRecognition {

ScreenCaptureEngine::ScreenCaptureEngine() 
    : screen_dc_(nullptr)
    , memory_dc_(nullptr)
    , bitmap_(nullptr)
    , screen_width_(0)
    , screen_height_(0)
    , optimization_level_(2)  // 默认速度优先
    , buffer_size_(0)
    , avg_capture_time_(0.0)
    , capture_count_(0) {
}

ScreenCaptureEngine::~ScreenCaptureEngine() {
    cleanupGDI();
}

bool ScreenCaptureEngine::initialize() {
    // 获取屏幕分辨率
    screen_width_ = GetSystemMetrics(SM_CXSCREEN);
    screen_height_ = GetSystemMetrics(SM_CYSCREEN);
    
    if (screen_width_ <= 0 || screen_height_ <= 0) {
        return false;
    }
    
    // 初始化GDI
    if (!initializeGDI()) {
        return false;
    }
    
    // 预分配缓冲区
    buffer_size_ = screen_width_ * screen_height_ * 4; // RGBA
    buffer_ = std::make_unique<uint8_t[]>(buffer_size_);
    
    return true;
}

bool ScreenCaptureEngine::initializeGDI() {
    // 获取屏幕DC
    screen_dc_ = GetDC(nullptr);
    if (!screen_dc_) {
        return false;
    }
    
    // 创建兼容DC
    memory_dc_ = CreateCompatibleDC(screen_dc_);
    if (!memory_dc_) {
        ReleaseDC(nullptr, screen_dc_);
        return false;
    }
    
    // 设置位图信息
    ZeroMemory(&bitmap_info_, sizeof(BITMAPINFO));
    bitmap_info_.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bitmap_info_.bmiHeader.biWidth = screen_width_;
    bitmap_info_.bmiHeader.biHeight = -screen_height_; // 负值表示从上到下
    bitmap_info_.bmiHeader.biPlanes = 1;
    bitmap_info_.bmiHeader.biBitCount = 32;
    bitmap_info_.bmiHeader.biCompression = BI_RGB;
    
    return true;
}

void ScreenCaptureEngine::cleanupGDI() {
    if (bitmap_) {
        DeleteObject(bitmap_);
        bitmap_ = nullptr;
    }
    
    if (memory_dc_) {
        DeleteDC(memory_dc_);
        memory_dc_ = nullptr;
    }
    
    if (screen_dc_) {
        ReleaseDC(nullptr, screen_dc_);
        screen_dc_ = nullptr;
    }
}

cv::Mat ScreenCaptureEngine::captureRegion(const Region& region) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    cv::Mat result;
    
    // 根据优化级别选择不同的截图方法
    switch (optimization_level_) {
    case 0:
        result = captureRegionQuality(region);
        break;
    case 1:
        result = captureRegionBalanced(region);
        break;
    case 2:
    default:
        result = captureRegionFast(region);
        break;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    updateCaptureStats(duration.count() / 1000.0);
    
    return result;
}

cv::Mat ScreenCaptureEngine::captureRegionFast(const Region& region) {
    // 速度优先模式 - 直接截取指定区域，避免全屏截图

    // 边界检查
    int x = std::max(0, std::min(region.x, screen_width_ - 1));
    int y = std::max(0, std::min(region.y, screen_height_ - 1));
    int w = std::max(1, std::min(region.width, screen_width_ - x));
    int h = std::max(1, std::min(region.height, screen_height_ - y));

    // 创建区域位图信息
    BITMAPINFO region_bitmap_info;
    ZeroMemory(&region_bitmap_info, sizeof(BITMAPINFO));
    region_bitmap_info.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    region_bitmap_info.bmiHeader.biWidth = w;
    region_bitmap_info.bmiHeader.biHeight = -h; // 负值表示从上到下
    region_bitmap_info.bmiHeader.biPlanes = 1;
    region_bitmap_info.bmiHeader.biBitCount = 32;
    region_bitmap_info.bmiHeader.biCompression = BI_RGB;

    // 创建区域位图
    void* bits = nullptr;
    HBITMAP bitmap = CreateDIBSection(memory_dc_, &region_bitmap_info, DIB_RGB_COLORS, &bits, nullptr, 0);
    if (!bitmap || !bits) {
        return cv::Mat();
    }

    // 选择位图到DC
    HBITMAP old_bitmap = (HBITMAP)SelectObject(memory_dc_, bitmap);

    // 直接截取指定区域（性能优化）
    if (!BitBlt(memory_dc_, 0, 0, w, h, screen_dc_, x, y, SRCCOPY)) {
        SelectObject(memory_dc_, old_bitmap);
        DeleteObject(bitmap);
        return cv::Mat();
    }

    // 创建OpenCV Mat
    cv::Mat bgra_image(h, w, CV_8UC4, bits);

    // 转换为BGR格式
    cv::Mat result;
    cv::cvtColor(bgra_image, result, cv::COLOR_BGRA2BGR);

    // 清理
    SelectObject(memory_dc_, old_bitmap);
    DeleteObject(bitmap);

    return result;
}

cv::Mat ScreenCaptureEngine::captureRegionBalanced(const Region& region) {
    // 平衡模式 - 在速度和质量之间平衡
    cv::Mat fast_result = captureRegionFast(region);
    
    if (fast_result.empty()) {
        return fast_result;
    }
    
    // 轻微的图像增强
    cv::Mat enhanced;
    cv::convertScaleAbs(fast_result, enhanced, 1.1, 5); // 轻微增强对比度和亮度
    
    return enhanced;
}

cv::Mat ScreenCaptureEngine::captureRegionQuality(const Region& region) {
    // 质量优先模式 - 包含图像增强和降噪
    cv::Mat balanced_result = captureRegionBalanced(region);
    
    if (balanced_result.empty()) {
        return balanced_result;
    }
    
    // 高质量图像处理
    cv::Mat denoised, sharpened;
    
    // 降噪
    cv::bilateralFilter(balanced_result, denoised, 5, 50, 50);
    
    // 锐化
    cv::Mat kernel = (cv::Mat_<float>(3, 3) << 
        0, -1, 0,
        -1, 5, -1,
        0, -1, 0);
    cv::filter2D(denoised, sharpened, -1, kernel);
    
    return sharpened;
}

std::vector<cv::Mat> ScreenCaptureEngine::captureRegions(const std::vector<Region>& regions) {
    std::vector<cv::Mat> results;
    results.reserve(regions.size());
    
    // 并行截图（如果区域数量较多）
    if (regions.size() > 4) {
        std::vector<std::future<cv::Mat>> futures;
        futures.reserve(regions.size());
        
        for (const auto& region : regions) {
            futures.emplace_back(std::async(std::launch::async, [this, region]() {
                return captureRegion(region);
            }));
        }
        
        for (auto& future : futures) {
            results.emplace_back(future.get());
        }
    } else {
        // 串行截图（区域较少时避免线程开销）
        for (const auto& region : regions) {
            results.emplace_back(captureRegion(region));
        }
    }
    
    return results;
}

std::pair<int, int> ScreenCaptureEngine::getScreenResolution() const {
    return {screen_width_, screen_height_};
}

void ScreenCaptureEngine::setOptimizationLevel(int level) {
    optimization_level_ = std::clamp(level, 0, 2);
}

void ScreenCaptureEngine::updateCaptureStats(double time_ms) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    capture_count_++;
    avg_capture_time_ = (avg_capture_time_ * (capture_count_ - 1) + time_ms) / capture_count_;
}

} // namespace WeaponRecognition
