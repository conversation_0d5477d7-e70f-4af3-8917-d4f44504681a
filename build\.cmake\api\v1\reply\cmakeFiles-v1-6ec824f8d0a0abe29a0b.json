{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/Temp_code/build/CMakeFiles/3.23.0/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/Temp_code/build/CMakeFiles/3.23.0/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/Temp_code/build/CMakeFiles/3.23.0/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/Temp_code/build/CMakeFiles/3.23.0/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCommonLanguageInclude.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/Desktop/Temp_code/build", "source": "C:/Users/<USER>/Desktop/Temp_code/cpp_weapon_recognition"}, "version": {"major": 1, "minor": 0}}