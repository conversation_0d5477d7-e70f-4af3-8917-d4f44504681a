#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的Lua代码解密工具
用于完全解密混淆的Lua代码，包括所有ASCII转义序列
"""

import re

def decode_all_ascii_sequences(text):
    """解码所有ASCII转义序列"""
    def replace_ascii(match):
        ascii_sequence = match.group(1)
        result = ""
        # 分割反斜杠分隔的数字
        parts = ascii_sequence.split('\\')
        for part in parts:
            if part.strip() and part.isdigit():
                try:
                    result += chr(int(part))
                except ValueError:
                    continue
        return result
    
    # 匹配所有形如 \116\111\110\117\109\98\101\114 的模式
    pattern = r'\\(\d+(?:\\\d+)*)'
    
    # 先处理引号内的转义序列
    def process_quoted_strings(match):
        quoted_content = match.group(1)
        decoded = re.sub(pattern, replace_ascii, quoted_content)
        return f'"{decoded}"'
    
    # 处理引号内的内容
    text = re.sub(r'"([^"]*)"', process_quoted_strings, text)
    
    # 处理剩余的转义序列
    text = re.sub(pattern, replace_ascii, text)
    
    return text

def format_lua_code_advanced(code):
    """高级Lua代码格式化"""
    # 基本替换
    code = code.replace(';', ';\n')
    code = code.replace('{', '{\n')
    code = code.replace('}', '\n}')
    
    # 在关键字前添加换行
    keywords = [
        'local ', 'function ', 'if ', 'then', 'else', 'elseif',
        'while ', 'for ', 'do', 'end', 'return', 'break'
    ]
    
    for keyword in keywords:
        code = code.replace(keyword, '\n' + keyword)
    
    # 处理操作符周围的空格
    operators = ['=', '==', '~=', '<=', '>=', '<', '>', '+', '-', '*', '/', '%']
    for op in operators:
        code = code.replace(op, f' {op} ')
    
    # 清理多余的空格和空行
    lines = []
    indent_level = 0
    
    for line in code.split('\n'):
        line = line.strip()
        if not line:
            continue
            
        # 简单的缩进处理
        if any(keyword in line for keyword in ['end', '}', 'else', 'elseif']):
            indent_level = max(0, indent_level - 1)
        
        lines.append('    ' * indent_level + line)
        
        if any(keyword in line for keyword in ['function', 'if', 'while', 'for', 'do', '{', 'then', 'else']):
            indent_level += 1
    
    return '\n'.join(lines)

def analyze_code_functionality(code):
    """分析代码功能"""
    functions_found = []
    
    # 检查常见的游戏相关函数
    game_functions = {
        'tonumber': '数字转换函数',
        'GetRunningTime': '获取运行时间 - 用于计时',
        'MoveMouse': '鼠标移动控制',
        'IsMouseButtonPressed': '鼠标按键检测',
        'PressKey': '按键模拟',
        'ReleaseKey': '释放按键',
        'Sleep': '延时函数',
        'apply_recoil': '后坐力补偿函数',
        'calculate_recoil_multiplier': '后坐力倍数计算',
        'read_weapon_from_file': '从文件读取武器信息',
        'is_authorized': '授权验证函数',
        'autopick': '自动拾取功能',
        'OnEvent': '事件处理函数',
        'EnablePrimaryMouseButtonEvents': '启用鼠标主键事件'
    }
    
    for func, desc in game_functions.items():
        if func in code:
            functions_found.append(f"✓ {desc}")
    
    return functions_found

def main():
    # 从文件读取最后一行的加密代码
    try:
        with open('开镜50垂直1（垂直可随意修改）.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            encrypted_line = lines[-1].strip()
    except FileNotFoundError:
        print("错误：找不到源文件")
        return
    
    print("="*80)
    print("Lua代码完整解密工具")
    print("="*80)
    print(f"原始加密代码长度: {len(encrypted_line)} 字符")
    
    # 第一步：完全解码ASCII转义序列
    print("\n步骤1: 解码所有ASCII转义序列...")
    decoded = decode_all_ascii_sequences(encrypted_line)
    
    # 第二步：高级格式化
    print("步骤2: 高级代码格式化...")
    formatted = format_lua_code_advanced(decoded)
    
    # 保存完全解密的代码
    with open('fully_decrypted_code.lua', 'w', encoding='utf-8') as f:
        f.write(formatted)
    
    print(f"\n完全解密的代码已保存到 'fully_decrypted_code.lua'")
    
    # 分析代码功能
    print("\n" + "="*80)
    print("代码功能分析:")
    print("="*80)
    
    functions = analyze_code_functionality(formatted)
    for func in functions:
        print(func)
    
    print(f"\n代码总行数: {len(formatted.split())}")
    print(f"解密后代码长度: {len(formatted)} 字符")
    
    # 显示部分解密后的代码
    print("\n" + "="*80)
    print("解密后代码预览 (前50行):")
    print("="*80)
    
    preview_lines = formatted.split('\n')[:50]
    for i, line in enumerate(preview_lines, 1):
        print(f"{i:3d}: {line}")
    
    if len(formatted.split('\n')) > 50:
        print(f"... (还有 {len(formatted.split('\n')) - 50} 行)")
    
    print("\n" + "="*80)
    print("总结:")
    print("="*80)
    print("这是一个游戏辅助脚本的核心代码，主要功能包括：")
    print("1. 武器后坐力自动补偿")
    print("2. 鼠标宏控制")
    print("3. 游戏事件监听和处理")
    print("4. 自动拾取功能")
    print("5. 授权验证机制")
    print("\n注意：此类代码通常用于游戏外挂，使用时请遵守游戏规则。")

if __name__ == "__main__":
    main()
