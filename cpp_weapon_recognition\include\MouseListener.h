#pragma once

#include "Types.h"
#include <functional>
#include <windows.h>

namespace WeaponRecognition {

class MouseListener {
public:
    using MouseCallback = std::function<void(const MouseEvent&)>;

    MouseListener();
    ~MouseListener();

    // 设置回调函数
    void setCallback(MouseCallback callback) { callback_ = callback; }
    
    // 开始监听
    bool startListening();
    
    // 停止监听
    void stopListening();

private:
    MouseCallback callback_;
    HHOOK hook_handle_;
    bool is_listening_;
    static MouseListener* instance_;
    
    // Windows消息处理
    static LRESULT CALLBACK mouseProc(int nCode, WPARAM wParam, LPARAM lParam);
    
    // 内部方法
    void handleMouseEvent(WPARAM wParam, LPARAM lParam);
};

} // namespace WeaponRecognition
