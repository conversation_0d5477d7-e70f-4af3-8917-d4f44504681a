﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{CB8FE29A-3CF2-3498-BE50-BEF57858FCCD}"
	ProjectSection(ProjectDependencies) = postProject
		{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F} = {CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC} = {14DBFB31-C996-30C9-9EE1-C04F40965FFC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "WeaponRecognition", "WeaponRecognition.vcxproj", "{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}"
	ProjectSection(ProjectDependencies) = postProject
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC} = {14DBFB31-C996-30C9-9EE1-C04F40965FFC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{14DBFB31-C996-30C9-9EE1-C04F40965FFC}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CB8FE29A-3CF2-3498-BE50-BEF57858FCCD}.Debug|x64.ActiveCfg = Debug|x64
		{CB8FE29A-3CF2-3498-BE50-BEF57858FCCD}.Release|x64.ActiveCfg = Release|x64
		{CB8FE29A-3CF2-3498-BE50-BEF57858FCCD}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CB8FE29A-3CF2-3498-BE50-BEF57858FCCD}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}.Debug|x64.ActiveCfg = Debug|x64
		{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}.Debug|x64.Build.0 = Debug|x64
		{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}.Release|x64.ActiveCfg = Release|x64
		{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}.Release|x64.Build.0 = Release|x64
		{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CFE86C41-6D20-3BC1-B94A-EBEC96BF133F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC}.Debug|x64.ActiveCfg = Debug|x64
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC}.Debug|x64.Build.0 = Debug|x64
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC}.Release|x64.ActiveCfg = Release|x64
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC}.Release|x64.Build.0 = Release|x64
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{14DBFB31-C996-30C9-9EE1-C04F40965FFC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BDCDFE89-4824-3710-9A5B-11498D21B7F9}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
