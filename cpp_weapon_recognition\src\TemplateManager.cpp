#include "TemplateManager.h"
#include "Utils.h"
#include <filesystem>
#include <iostream>

namespace WeaponRecognition {

bool TemplateManager::loadTemplates(const std::string& template_path) {
    std::cout << "正在加载模板文件: " << template_path << std::endl;
    
    // 定义模板类别
    std::vector<std::string> categories = {
        "weapons", "muzzles", "grips", "scopes", "stocks", "poses", "car"
    };
    
    int total_loaded = 0;
    
    for (const auto& category : categories) {
        std::string category_path = template_path + "/" + category;
        
        if (loadCategoryTemplates(category_path, category)) {
            std::cout << "  ✅ " << category << ": " << templates_[category].size() << " 个模板" << std::endl;
            total_loaded += templates_[category].size();
        } else {
            std::cout << "  ⚠️ " << category << ": 文件夹不存在或为空" << std::endl;
        }
    }
    
    std::cout << "✅ 模板加载完成！总共 " << total_loaded << " 个模板文件" << std::endl;
    return total_loaded > 0;
}

bool TemplateManager::loadCategoryTemplates(const std::string& category_path, const std::string& category_name) {
    try {
        if (!std::filesystem::exists(category_path)) {
            return false;
        }
        
        templates_[category_name] = TemplateMap();
        
        for (const auto& entry : std::filesystem::directory_iterator(category_path)) {
            if (entry.is_regular_file()) {
                std::string file_path = entry.path().string();
                std::string extension = entry.path().extension().string();
                
                if (extension == ".png" || extension == ".jpg" || extension == ".jpeg") {
                    std::string template_name = entry.path().stem().string();
                    cv::Mat template_img = loadTemplate(file_path);
                    
                    if (!template_img.empty()) {
                        templates_[category_name][template_name] = template_img;
                    }
                }
            }
        }
        
        return !templates_[category_name].empty();
    } catch (const std::exception& e) {
        std::cerr << "加载模板类别失败 " << category_name << ": " << e.what() << std::endl;
        return false;
    }
}

cv::Mat TemplateManager::loadTemplate(const std::string& file_path) {
    cv::Mat template_img = cv::imread(file_path, cv::IMREAD_GRAYSCALE);
    
    if (template_img.empty()) {
        std::cerr << "无法加载模板文件: " << file_path << std::endl;
    }
    
    return template_img;
}

const TemplateMap& TemplateManager::getTemplates(const std::string& category) const {
    static TemplateMap empty_map;
    
    auto it = templates_.find(category);
    if (it != templates_.end()) {
        return it->second;
    }
    
    return empty_map;
}

bool TemplateManager::hasTemplate(const std::string& category, const std::string& name) const {
    auto category_it = templates_.find(category);
    if (category_it == templates_.end()) {
        return false;
    }
    
    return category_it->second.find(name) != category_it->second.end();
}

size_t TemplateManager::getTemplateCount() const {
    size_t total = 0;
    for (const auto& category : templates_) {
        total += category.second.size();
    }
    return total;
}

} // namespace WeaponRecognition
