#pragma once

#include <string>
#include <chrono>
#include <vector>

namespace WeaponRecognition {

class Utils {
public:
    // 获取当前时间字符串
    static std::string getCurrentTimeString();
    
    // 获取高精度时间戳
    static double getHighResolutionTime();
    
    // 文件操作
    static bool fileExists(const std::string& path);
    static bool createDirectory(const std::string& path);
    static std::string getExecutableDirectory();
    
    // 字符串操作
    static std::string trim(const std::string& str);
    static std::vector<std::string> split(const std::string& str, char delimiter);
    
    // 性能测量
    class Timer {
    public:
        Timer() : start_time_(std::chrono::high_resolution_clock::now()) {}
        
        double elapsed() const {
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time_);
            return duration.count() / 1000.0; // 返回毫秒
        }
        
        void reset() {
            start_time_ = std::chrono::high_resolution_clock::now();
        }
        
    private:
        std::chrono::high_resolution_clock::time_point start_time_;
    };
};

} // namespace WeaponRecognition
