#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <opencv2/opencv.hpp>

namespace WeaponRecognition {

// 基础类型定义
using Region = cv::Rect;
using Template = cv::Mat;
using TemplateMap = std::unordered_map<std::string, Template>;
using RegionMap = std::unordered_map<std::string, Region>;

// 武器配置结构
struct WeaponConfig {
    std::string weapon_name;
    std::string muzzles;
    std::string grips;
    std::string scopes;
    std::string stocks;
    std::string poses = "None";
    double scope_zoom = 1.0;
    std::string car = "None";
    
    // 重置配置
    void reset() {
        weapon_name.clear();
        muzzles.clear();
        grips.clear();
        scopes.clear();
        stocks.clear();
        poses = "None";
        scope_zoom = 1.0;
        car = "None";
    }
    
    // 检查配置是否为空
    bool isEmpty() const {
        return weapon_name.empty();
    }
};

// 识别结果
struct RecognitionResult {
    std::string category;
    std::string result;
    double confidence;
    
    RecognitionResult() : confidence(0.0) {}
    RecognitionResult(const std::string& cat, const std::string& res, double conf)
        : category(cat), result(res), confidence(conf) {}
};

// 系统配置
struct SystemConfig {
    int screen_width = 1920;
    int screen_height = 1080;
    std::string template_path;
    RegionMap regions;
    double recognition_threshold = 0.6;
    int max_threads = 4;
    double zoom_step = 0.06;
    double max_zoom = 1.9;
    double min_zoom = 0.9;
};

// 鼠标事件类型
enum class MouseEventType {
    LEFT_DOWN,
    LEFT_UP,
    RIGHT_DOWN,
    RIGHT_UP,
    SCROLL_UP,
    SCROLL_DOWN
};

// 鼠标事件结构
struct MouseEvent {
    MouseEventType type;
    int x, y;
    int delta;  // 滚轮增量
};

// 热键事件类型
enum class HotkeyType {
    TRIGGER_RECOGNITION,    // ` 键
    SWITCH_RIFLE,          // 1 键
    SWITCH_SNIPER,         // 2 键
    EXIT_PROGRAM           // ESC 键
};

// 武器类型
enum class WeaponType {
    RIFLE,
    SNIPER
};

// 识别状态
enum class RecognitionStatus {
    IDLE,
    RECOGNIZING,
    COMPLETED,
    ERROR
};

// 性能统计
struct PerformanceStats {
    double avg_recognition_time = 0.0;
    double avg_capture_time = 0.0;
    double avg_template_match_time = 0.0;
    int total_recognitions = 0;
    int successful_recognitions = 0;
    
    void reset() {
        avg_recognition_time = 0.0;
        avg_capture_time = 0.0;
        avg_template_match_time = 0.0;
        total_recognitions = 0;
        successful_recognitions = 0;
    }
    
    double getSuccessRate() const {
        return total_recognitions > 0 ? 
               static_cast<double>(successful_recognitions) / total_recognitions : 0.0;
    }
};

} // namespace WeaponRecognition
