@echo off
echo ========================================
echo    PUBG武器识别系统 C++版 部署脚本
echo ========================================

:: 检查是否存在Python版本的nc文件夹
if not exist "..\nc" (
    echo 错误: 未找到nc文件夹，请确保Python版本在上级目录
    echo 请将此C++项目放在Python版本的同级目录下
    pause
    exit /b 1
)

:: 复制nc文件夹到当前目录
echo 正在复制模板文件...
if exist "nc" rmdir /s /q nc
xcopy "..\nc" "nc\" /E /I /Y >nul
if errorlevel 1 (
    echo 错误: 复制模板文件失败
    pause
    exit /b 1
)

:: 检查OpenCV环境
echo 正在检查OpenCV环境...
set OPENCV_FOUND=0

:: 检查常见的OpenCV安装路径
if exist "C:\opencv\build\x64\vc16\bin\opencv_world*.dll" (
    set OPENCV_PATH=C:\opencv\build\x64\vc16\bin
    set OPENCV_FOUND=1
)
if exist "C:\opencv\build\x64\vc15\bin\opencv_world*.dll" (
    set OPENCV_PATH=C:\opencv\build\x64\vc15\bin
    set OPENCV_FOUND=1
)
if exist "D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\bin\opencv_world*.dll" (
    set OPENCV_PATH=D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\bin
    set OPENCV_FOUND=1
)

if %OPENCV_FOUND%==0 (
    echo 警告: 未找到OpenCV，请手动设置环境变量
    echo 或者将OpenCV的bin目录添加到PATH中
    echo.
    echo 常见OpenCV路径:
    echo   C:\opencv\build\x64\vc16\bin
    echo   D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\bin
    echo.
) else (
    echo 找到OpenCV: %OPENCV_PATH%
    set PATH=%OPENCV_PATH%;%PATH%
)

:: 创建快速启动脚本
echo 正在创建快速启动脚本...
(
echo @echo off
echo title PUBG武器识别系统 C++版
echo cd /d "%%~dp0"
echo.
echo echo ========================================
echo echo    PUBG武器识别系统 C++版 - 高性能模式
echo echo ========================================
echo echo 预期性能: CPU占用 ^< 5%%, 内存占用 ^< 30MB
echo echo 响应延迟: ^< 20ms
echo echo.
echo.
if %OPENCV_FOUND%==1 (
    echo set PATH=%OPENCV_PATH%;%%PATH%%
)
echo.
echo if not exist "WeaponRecognition.exe" ^(
echo     echo 错误: 未找到可执行文件，请先运行build.bat构建项目
echo     pause
echo     exit /b 1
echo ^)
echo.
echo WeaponRecognition.exe
echo pause
) > "run.bat"

:: 创建性能监控脚本
echo 正在创建性能监控脚本...
(
echo @echo off
echo title 性能监控 - PUBG武器识别系统
echo echo 正在启动性能监控...
echo echo.
echo :loop
echo for /f "tokens=2 delims=," %%%%a in ^('tasklist /fi "imagename eq WeaponRecognition.exe" /fo csv ^| find "WeaponRecognition.exe"'^) do ^(
echo     echo [%%time%%] 程序运行中: %%%%a
echo ^)
echo timeout /t 5 /nobreak ^>nul
echo goto loop
) > "monitor.bat"

:: 创建配置备份脚本
echo 正在创建配置备份脚本...
(
echo @echo off
echo echo 正在备份配置文件...
echo if not exist "backup" mkdir backup
echo copy "config.json" "backup\config_%%date:~0,4%%%%date:~5,2%%%%date:~8,2%%.json" ^>nul
echo copy "C:\Temp\weapon.lua" "backup\weapon_%%date:~0,4%%%%date:~5,2%%%%date:~8,2%%.lua" ^>nul 2^>^&1
echo echo 配置文件已备份到backup文件夹
echo pause
) > "backup_config.bat"

:: 创建环境检查脚本
echo 正在创建环境检查脚本...
(
echo @echo off
echo title 环境检查 - PUBG武器识别系统
echo echo ========================================
echo echo           环境检查报告
echo echo ========================================
echo echo.
echo echo [系统信息]
echo systeminfo ^| findstr /C:"OS Name" /C:"Total Physical Memory"
echo echo.
echo echo [CPU信息]
echo wmic cpu get name,numberofcores,numberoflogicalprocessors /format:list ^| findstr "="
echo echo.
echo echo [OpenCV检查]
if %OPENCV_FOUND%==1 (
    echo echo ✅ OpenCV: 已找到 ^(%OPENCV_PATH%^)
) else (
    echo echo ❌ OpenCV: 未找到
)
echo echo.
echo echo [文件检查]
echo if exist "WeaponRecognition.exe" ^(echo ✅ 可执行文件: 存在^) else ^(echo ❌ 可执行文件: 不存在^)
echo if exist "config.json" ^(echo ✅ 配置文件: 存在^) else ^(echo ❌ 配置文件: 不存在^)
echo if exist "nc" ^(echo ✅ 模板文件夹: 存在^) else ^(echo ❌ 模板文件夹: 不存在^)
echo echo.
echo echo [性能测试]
echo echo 正在进行简单性能测试...
echo powershell -command "Measure-Command { Get-Process } | Select-Object TotalMilliseconds"
echo echo.
echo pause
) > "check_env.bat"

echo.
echo ========================================
echo           部署完成！
echo ========================================
echo.
echo 已创建的文件:
echo   ✅ nc/                    - 模板文件夹
echo   ✅ run.bat               - 快速启动脚本
echo   ✅ monitor.bat           - 性能监控脚本
echo   ✅ backup_config.bat     - 配置备份脚本
echo   ✅ check_env.bat         - 环境检查脚本
echo.
echo 下一步操作:
echo   1. 运行 build.bat 构建项目
echo   2. 运行 check_env.bat 检查环境
echo   3. 运行 run.bat 启动程序
echo.
echo 性能优化建议:
echo   - 关闭不必要的后台程序
echo   - 设置程序为高优先级
echo   - 确保游戏运行在主显示器
echo.

pause
