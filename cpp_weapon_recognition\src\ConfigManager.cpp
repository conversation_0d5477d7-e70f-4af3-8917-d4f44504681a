#include "ConfigManager.h"
#include "Utils.h"
#include <fstream>
#include <iostream>

namespace WeaponRecognition {

bool ConfigManager::loadConfig(int screen_width, int screen_height) {
    system_config_.screen_width = screen_width;
    system_config_.screen_height = screen_height;
    
    // 简化版：直接使用硬编码配置
    if (screen_width == 1920 && screen_height == 1080) {
        system_config_.template_path = "nc/1920x1080/weapon_templates";
        system_config_.regions = {
            {"poses", Region(711, 976, 30, 50)},
            {"weapon_name_rifle", Region(1371, 95, 70, 30)},
            {"weapon_name_sniper", Region(1371, 321, 70, 30)},
            {"muzzles_rifle", Region(1356, 242, 20, 20)},
            {"muzzles_sniper", Region(1356, 468, 20, 20)},
            {"grips_rifle", Region(1448, 253, 20, 20)},
            {"grips_sniper", Region(1448, 479, 20, 20)},
            {"scopes_rifle", Region(1631, 121, 20, 20)},
            {"scopes_sniper", Region(1631, 347, 20, 20)},
            {"stocks_rifle", Region(1765, 255, 20, 20)},
            {"stocks_sniper", Region(1765, 481, 20, 20)},
            {"car", Region(551, 1023, 13, 18)}
        };
    } else if (screen_width == 2560 && screen_height == 1440) {
        system_config_.template_path = "nc/2560x1440/weapon_templates";
        system_config_.regions = {
            {"poses", Region(948, 1301, 40, 67)},
            {"weapon_name_rifle", Region(1828, 127, 93, 40)},
            {"weapon_name_sniper", Region(1828, 428, 93, 40)},
            {"muzzles_rifle", Region(1808, 323, 27, 27)},
            {"muzzles_sniper", Region(1808, 624, 27, 27)},
            {"grips_rifle", Region(1931, 337, 27, 27)},
            {"grips_sniper", Region(1931, 639, 27, 27)},
            {"scopes_rifle", Region(2175, 161, 27, 27)},
            {"scopes_sniper", Region(2175, 463, 27, 27)},
            {"stocks_rifle", Region(2353, 340, 27, 27)},
            {"stocks_sniper", Region(2353, 641, 27, 27)},
            {"car", Region(735, 1364, 17, 24)}
        };
    } else {
        // 默认使用1920x1080配置
        system_config_.template_path = "nc/1920x1080/weapon_templates";
        system_config_.regions = {
            {"poses", Region(711, 976, 30, 50)},
            {"weapon_name_rifle", Region(1371, 95, 70, 30)},
            {"weapon_name_sniper", Region(1371, 321, 70, 30)},
            {"muzzles_rifle", Region(1356, 242, 20, 20)},
            {"muzzles_sniper", Region(1356, 468, 20, 20)},
            {"grips_rifle", Region(1448, 253, 20, 20)},
            {"grips_sniper", Region(1448, 479, 20, 20)},
            {"scopes_rifle", Region(1631, 121, 20, 20)},
            {"scopes_sniper", Region(1631, 347, 20, 20)},
            {"stocks_rifle", Region(1765, 255, 20, 20)},
            {"stocks_sniper", Region(1765, 481, 20, 20)},
            {"car", Region(551, 1023, 13, 18)}
        };
        std::cout << "警告: 使用默认1920x1080配置，当前分辨率: " 
                  << screen_width << "x" << screen_height << std::endl;
    }
    
    // 设置默认参数
    system_config_.recognition_threshold = 0.6;
    system_config_.max_threads = 4;
    system_config_.zoom_step = 0.06;
    system_config_.max_zoom = 1.9;
    system_config_.min_zoom = 0.9;
    
    return true;
}

bool ConfigManager::saveConfig(const std::string& config_path) {
    // 简化版：暂不实现保存功能
    return true;
}

bool ConfigManager::loadFromFile(const std::string& file_path) {
    // 简化版：暂不实现文件加载
    return true;
}

std::string ConfigManager::findConfigFile(int width, int height) {
    return "config.json";
}

bool ConfigManager::parseJsonConfig(const std::string& json_content) {
    // 简化版：暂不实现JSON解析
    return true;
}

} // namespace WeaponRecognition
