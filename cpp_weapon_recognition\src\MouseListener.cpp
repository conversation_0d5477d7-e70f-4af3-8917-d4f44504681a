#include "MouseListener.h"
#include <iostream>

namespace WeaponRecognition {

MouseListener* MouseListener::instance_ = nullptr;

MouseListener::MouseListener() : hook_handle_(nullptr), is_listening_(false) {
    instance_ = this;
}

MouseListener::~MouseListener() {
    stopListening();
    instance_ = nullptr;
}

bool MouseListener::startListening() {
    if (is_listening_) {
        return true;
    }
    
    // 安装鼠标钩子
    hook_handle_ = SetWindowsHookEx(WH_MOUSE_LL, mouseProc, GetModuleHandle(nullptr), 0);
    if (!hook_handle_) {
        std::cerr << "❌ 安装鼠标钩子失败" << std::endl;
        return false;
    }
    
    is_listening_ = true;
    std::cout << "✅ 鼠标监听已启动" << std::endl;
    return true;
}

void MouseListener::stopListening() {
    if (hook_handle_) {
        UnhookWindowsHookEx(hook_handle_);
        hook_handle_ = nullptr;
    }
    
    is_listening_ = false;
    std::cout << "✅ 鼠标监听已停止" << std::endl;
}

LRESULT CALLBACK MouseListener::mouseProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0 && instance_) {
        instance_->handleMouseEvent(wParam, lParam);
    }
    
    return CallNextHookEx(nullptr, nCode, wParam, lParam);
}

void MouseListener::handleMouseEvent(WPARAM wParam, LPARAM lParam) {
    if (!callback_) {
        return;
    }
    
    MSLLHOOKSTRUCT* mouse_data = (MSLLHOOKSTRUCT*)lParam;
    MouseEvent event;
    event.x = mouse_data->pt.x;
    event.y = mouse_data->pt.y;
    event.delta = 0;
    
    switch (wParam) {
    case WM_LBUTTONDOWN:
        event.type = MouseEventType::LEFT_DOWN;
        callback_(event);
        break;
        
    case WM_LBUTTONUP:
        event.type = MouseEventType::LEFT_UP;
        callback_(event);
        break;
        
    case WM_RBUTTONDOWN:
        event.type = MouseEventType::RIGHT_DOWN;
        std::cout << "🎯 右键按下 - 开镜检测" << std::endl;
        callback_(event);
        break;
        
    case WM_RBUTTONUP:
        event.type = MouseEventType::RIGHT_UP;
        std::cout << "🎯 右键释放" << std::endl;
        callback_(event);
        break;
        
    case WM_MOUSEWHEEL:
        {
            short delta = GET_WHEEL_DELTA_WPARAM(mouse_data->mouseData);
            event.delta = delta;
            
            if (delta > 0) {
                event.type = MouseEventType::SCROLL_UP;
                std::cout << "🔍 滚轮向上" << std::endl;
            } else {
                event.type = MouseEventType::SCROLL_DOWN;
                std::cout << "🔍 滚轮向下" << std::endl;
            }
            
            callback_(event);
        }
        break;
    }
}

} // namespace WeaponRecognition
