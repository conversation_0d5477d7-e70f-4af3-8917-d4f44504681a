#include "HotkeyManager.h"
#include <iostream>

namespace WeaponRecognition {

HotkeyManager* HotkeyManager::instance_ = nullptr;

HotkeyManager::HotkeyManager() : hook_handle_(nullptr) {
    instance_ = this;
}

HotkeyManager::~HotkeyManager() {
    unregisterAllHotkeys();
    instance_ = nullptr;
}

bool HotkeyManager::registerHotkey(HotkeyType type, int vk_code) {
    // 存储热键映射
    hotkey_map_[vk_code] = type;
    
    // 安装键盘钩子（如果还没有安装）
    if (!hook_handle_) {
        hook_handle_ = SetWindowsHookEx(WH_KEYBOARD_LL, hotkeyProc, GetModuleHandle(nullptr), 0);
        if (!hook_handle_) {
            std::cerr << "❌ 安装键盘钩子失败" << std::endl;
            return false;
        }
    }
    
    std::cout << "✅ 热键注册成功: " << getHotkeyName(type) << " -> " << getKeyName(vk_code) << std::endl;
    return true;
}

bool HotkeyManager::unregisterHotkey(HotkeyType type) {
    // 查找并移除热键
    for (auto it = hotkey_map_.begin(); it != hotkey_map_.end(); ++it) {
        if (it->second == type) {
            hotkey_map_.erase(it);
            std::cout << "✅ 热键注销成功: " << getHotkeyName(type) << std::endl;
            return true;
        }
    }
    return false;
}

void HotkeyManager::unregisterAllHotkeys() {
    if (hook_handle_) {
        UnhookWindowsHookEx(hook_handle_);
        hook_handle_ = nullptr;
    }
    
    hotkey_map_.clear();
    std::cout << "✅ 所有热键已注销" << std::endl;
}

LRESULT CALLBACK HotkeyManager::hotkeyProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0 && instance_) {
        if (wParam == WM_KEYDOWN) {
            KBDLLHOOKSTRUCT* kbd = (KBDLLHOOKSTRUCT*)lParam;
            instance_->handleHotkey(kbd->vkCode);
        }
    }
    
    return CallNextHookEx(nullptr, nCode, wParam, lParam);
}

void HotkeyManager::handleHotkey(int vk_code) {
    auto it = hotkey_map_.find(vk_code);
    if (it != hotkey_map_.end() && callback_) {
        std::cout << "🎯 热键触发: " << getHotkeyName(it->second) << std::endl;
        callback_(it->second);
    }
}

std::string HotkeyManager::getHotkeyName(HotkeyType type) {
    switch (type) {
    case HotkeyType::TRIGGER_RECOGNITION: return "触发识别";
    case HotkeyType::SWITCH_RIFLE: return "切换步枪";
    case HotkeyType::SWITCH_SNIPER: return "切换狙击枪";
    case HotkeyType::EXIT_PROGRAM: return "退出程序";
    default: return "未知";
    }
}

std::string HotkeyManager::getKeyName(int vk_code) {
    switch (vk_code) {
    case VK_OEM_3: return "`键";  // 波浪号键
    case '1': return "1键";
    case '2': return "2键";
    case VK_ESCAPE: return "ESC键";
    default: return "键码" + std::to_string(vk_code);
    }
}

} // namespace WeaponRecognition
